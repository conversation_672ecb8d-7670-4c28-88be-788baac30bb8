<?php 
require_once("Home.php"); // including home controller

class Agents extends Home
{
	public $user_id;
	public $is_agent;    
    public function __construct()
    {
        parent::__construct();
		
		if ($this->session->userdata('logged_in')!= 1) {
            redirect('home/login', 'location');
        }				
		$this->is_agent= $this->session->userdata('is_agent');
		$this->source = $this->session->userdata('source');
		
		if ($this->is_agent!= '1') {
            redirect('facebook_rx_account_import/index', 'location');
        }
		
		$this->load->helper('form');
        $this->load->library('upload');
		
        $this->user_id=$this->session->userdata('user_id');

     
        $this->important_feature();
    }
	
	public function index()
    {
		try{
			$this->load->database();
			$this->load->library('grocery_CRUD');
			$crud = new grocery_CRUD();

			$crud->set_theme('flexigrid');
			$crud->set_table('users');
			$crud->order_by('id');
			$crud->where('users.deleted', '0');
			$crud->where('users.source =', $this->source);
			$crud->set_subject($this->lang->line("user"));
			$crud->set_relation('package_id','package','package_name',array('package.deleted' => '0'));
			
			$crud->columns('name', 'email','package_id', 'status', 'add_date','last_login_at','expired_date', 'vat_no');
			$crud->field_type('expired_date', 'date');
			
			$crud->display_as('add_date',$this->lang->line('Register date'));
			$crud->display_as('last_login_at',$this->lang->line('Last Logged in'));
			//$crud->display_as('last_login_ip',$this->lang->line('Last IP'));
			$crud->display_as('name', $this->lang->line('name'));
			$crud->display_as('email', $this->lang->line('email'));
			//$crud->display_as('mobile', $this->lang->line('mobile'));
			//$crud->display_as('address', $this->lang->line('address'));
			$crud->display_as('status', $this->lang->line('status'));
			//$crud->display_as('user_type', $this->lang->line('Type'));
			//$crud->display_as('password', $this->lang->line('password'));
			$crud->display_as('package_id', $this->lang->line('package name'));
			$crud->display_as('expired_date', $this->lang->line('expiry date'));
			$crud->display_as('vat_no', $this->lang->line('settings'));
			
			$crud->unset_add();
            $crud->unset_edit();
			$crud->unset_delete();
			$crud->unset_read();
			$crud->unset_print();
			$crud->unset_export();
			
			$crud->callback_column('expired_date', array($this, 'expired_date_display_crud'));
			$crud->callback_column('add_date', array($this, 'expired_date_display_crud'));
			$crud->callback_column('last_login_at', array($this, 'date_display_crud'));
			$crud->callback_column('status', array($this, 'status_display_crud'));
			

			$crud->add_action($this->lang->line('Extend'), 'fa fa-plus', 'agents/extend');
			$crud->add_action($this->lang->line('Password'), 'fas fa-key', 'agents/password');
			$crud->add_action($this->lang->line('Clean Usage'), 'fa fa-list', 'agents/clear_usage_action');
			$crud->add_action($this->lang->line('A'), 'fas fa-at', 'agents/set_a_action');
			$crud->add_action($this->lang->line('B'), 'fas fa-bold', 'agents/set_b_action');
			$crud->add_action($this->lang->line('X'), 'fa fa-times', 'agents/set_x_action');
			$crud->add_action($this->lang->line('W'), 'fab fa-wikipedia-w', 'agents/set_w_action');
			$crud->add_action($this->lang->line('K'), 'fa-solid fa-k', 'agents/set_k_action');
			$crud->add_action($this->lang->line('L'), 'fa-solid fa-l', 'agents/set_l_action');
			
			$output = $crud->render();
			$data['output']=$output;
			$data['page_title'] = $this->lang->line("user management");
			$data['crud']=1;

			$this->_viewcontroller($data);

		}
		catch(Exception $e){
			show_error($e->getMessage().' --- '.$e->getTraceAsString());
		}			
    }
	
	public function pages()
	{
		try
		{
			$this->load->database();
			$this->load->library('grocery_CRUD');
			
			$crud = new grocery_CRUD();

			$crud->set_theme('flexigrid');
			$crud->set_table('facebook_rx_fb_page_info');
			$crud->where('facebook_rx_fb_page_info.deleted', '0');
			$crud->where('source =', $this->source);
			$crud->set_relation('user_id','users','name',array('users.deleted' => '0'));
			$crud->set_subject($this->lang->line("pages"));
			$crud->columns('user_id', 'page_id','page_name','add_date');
			$crud->display_as('user_id', $this->lang->line('name'));
			$crud->display_as('add_date',$this->lang->line('Register date'));
			$crud->display_as('page_name', $this->lang->line('Page Name'));
			$crud->display_as('page_id', $this->lang->line('Id'));
			$crud->unset_add();
            $crud->unset_edit();
			$crud->unset_delete();
			$crud->unset_read();
			$crud->unset_print();
			$crud->unset_export();
			
			$crud->callback_column('add_date', array($this, 'expired_date_display_crud'));
			$crud->callback_column('page_id', array($this, 'page_id_display_crud'));
			
			$output = $crud->render();
			$data['output']=$output;
			$data['page_title'] = $this->lang->line("pages management");
			$data['crud']=1;

			$this->_viewcontroller($data);
		}
		catch(Exception $e){
			show_error($e->getMessage().' --- '.$e->getTraceAsString());
		}
	}
	public function status_display_crud($value, $row)
    {
        if ($value == 1) {
            return "<span class='label label-light'><i class='fa fa-check-circle green'></i> ".$this->lang->line('active')."</sapn>";
        } else {
            return "<span class='label label-light'><i class='fa fa-remove red'></i> ".$this->lang->line('inactive')."</sapn>";
        }
    }
	
	public function expired_date_display_crud($value, $row)
    {
        if($row->user_type=="Admin") return "N/A";
        if ($value == '0000-00-00 00:00:00') {
            $value = "-";
        }
        else $value=date("d-m-Y",strtotime($value));
        return $value;
    }

    public function date_display_crud($value, $row)
    {
        if ($value == '0000-00-00 00:00:00') 
        $value = "-";       
        else $value=date("d-m-Y H:i",strtotime($value));
        return $value;
    }

	public function users_list($output = null)
	{
		$this->load->view('agents/list.php',(array)$output);
	}
	
	public function page_id_display_crud($value, $row)
    {
        $value = "<a href='https://fb.com/$value'>$value</a>";
        return $value;
    }
	
	public function extend($id)
    {
        $this->session->set_userdata('extend_user_id', $id);

        $table = 'users';
        $where['where'] = array('id' => $id);

        $info = $this->basic->get_data($table, $where);

        $data['user_name'] = $info[0]['name'];

        $data['body'] = 'agents/extend';
        $data['page_title'] =  $this->lang->line("extend");
        $this->_viewcontroller($data);
    }
	
	public function password($id)
    {
        $this->session->set_userdata('password_user_id', $id);

        $table = 'users';
        $where['where'] = array('id' => $id);

        $info = $this->basic->get_data($table, $where);

        $data['user_name'] = $info[0]['name'];
		$data['email'] = $info[0]['email'];
		
        $newPassword = "";
        if (strlen($id) >= 4)
        {
            $newPassword = $id;
        }
        else
        {
            $newPassword = $id . $id;
        }
		
		//reset password
		$table = 'users';
		$where = array('id' => $id);

		$up_data=array
		 (
			 "password"=>md5($newPassword),
		 );

		$this->basic->update_data($table, $where, $up_data);
		
        $accountResult = "اسم المستخدم : \n" .
                        $data['email']."\n".
                        "كلمة المرور : \n".
                        $newPassword."\n" .
                        "=============================================\n";		
		$data['generated_password'] = $accountResult;
        $data['body'] = 'agents/password';
        $data['page_title'] =  $this->lang->line("password");
        $this->_viewcontroller($data);
    }
	
	public function extend_action()
    {

        if ($_SERVER['REQUEST_METHOD'] === 'GET') {
            redirect('home/access_forbidden', 'location');
        }
		
		$agent = $this->session->userdata('source');
		if($agent !== 'waleed')
		{
			$this->session->set_flashdata('error_message', 1);
			redirect('agents/', 'location');
		}
        $id = $this->session->userdata('extend_user_id');
		
        if ($_POST) {
            $this->form_validation->set_rules('days', '<b>'. $this->lang->line("days").'</b>', 'trim|required|callback_days_check');
        }
        if ($this->form_validation->run() == false) {
            $this->extend($id);
        } else {
            $days = $this->input->post('days', true);
			$new_days = intval($days);
						
			//check if user extend this month
			$month = date('n');
			$year = date('Y');

			$extend_result = $this->db->get_where('extend_history', array("user_id" => $id,"month" => $month,"year" => $year));
			$extend_info =$extend_result->result();

			$current_days = $extend_info[0]->days;
;
			if (($current_days +$new_days) > 5)
			{
				$this->session->set_flashdata('error_message', 1);
				$this->extend($id);
			}
			else
			{
				//get current expired date
				$table = 'users';
				$where['where'] = array('id' => $id);
				$info = $this->basic->get_data($table, $where);
				$expire_date = $info[0]['expired_date'];
				
				//extend days action
				$table_extend = 'users';
				$where_extend = array('id' => $id);

				$date = strtotime($expire_date);
				echo "date is : ". date('Y-m-d', $date) ."<br>";
				echo "current date is : ". date('Y-m-d') . "<br>";
				echo "new days: $new_days <br>";
				//die();
				if(date('Y-m-d', $date) < date('Y-m-d') && $new_days >= 0)
				{
					$date = strtotime(date('Y-m-d'));
				}
				
				$date = strtotime("+$new_days day", $date);
				/*
				if($new_days >=0)
				{
					$date = strtotime("+$new_days day", $date);
				}
				else
				{
					$date = strtotime("-$new_days day", $date);
				}
				*/
				$date = date('Y-m-d', $date);
				//echo "date is : ". $date . "<br>";
				//die();
				$data=array
				 (
					 "expired_date"=>$date,
				 );

				$this->basic->update_data($table_extend, $where_extend, $data);
				
				//insert in extend history
				$month = date('n');
				$year = date('Y');
				
				$extend_where=array("user_id"=>$id,"month"=>$month,"year"=>$year);

				$insert_data=array("user_id"=>$id,"month"=>$month,"year"=>$year,"days"=>$days);

				if($this->basic->is_exist("extend_history",$extend_where))
				{
					$this->db->set('days', 'days+'.$new_days, FALSE);
					$this->db->where($extend_where);
					$this->db->update('extend_history');

				}
				else $this->basic->insert_data("extend_history",$insert_data);
				
				$this->session->set_flashdata('success_message', 1);
				redirect('agents/', 'location');
			}
        }
    }
	public function clean($id)
	{
        $table = 'users';
        $where['where'] = array('id' => $id);

        $info = $this->basic->get_data($table, $where);

        $data['user_name'] = $info[0]['name'];

        $data['body'] = 'agents/clean';
        $data['page_title'] =  $this->lang->line("Clean Usage");
        $this->_viewcontroller($data);
	}
	
	public function clear_usage_action($id)
    {
							

		$month = date('n');
		$year = date('Y');
		//check if user clean this month
		
		$clean_result = $this->db->get_where('extend_history', array("user_id" => $id,"month" => $month,"year" => $year));
		$clean_info =$clean_result->result();

		$clean = $clean_info[0]->clean;
;
		if ($clean == 1)
		{
			$this->session->set_flashdata('error_message', 1);
			$this->clean($id);
		}
		else
		{
			//get current usage log
			$table = 'usage_log';
			$where['where'] = array(
			'user_id' => $id,
			'usage_month' => $month,
			'usage_year' => $year,
			'module_id' => '80'
			);
			$info = $this->basic->get_data($table, $where);
			$usage_count = $info[0]['usage_count'];
			
			if( ($usage_count - 5) < 0 )
			{
				$usage_count = 0;
			}
			else
			{
				$usage_count = $usage_count - 5;
			}
			//clean usage logs action
			$table_clean = 'usage_log';
			$where_clean =  array(
			'user_id' => $id,
			'usage_month' => $month,
			'usage_year' => $year,
			'module_id' => '80'
			);

			$data=array
			 (
				 "usage_count"=>$usage_count,
			 );

			$this->basic->update_data($table_clean, $where_clean, $data);
			
			//insert in clean history
			
			$month = date('n');
			$year = date('Y');
			
			$clean_where=array("user_id"=>$id,"month"=>$month,"year"=>$year);

			$insert_data=array("user_id"=>$id,"month"=>$month,"year"=>$year,"clean"=>'1');

			if($this->basic->is_exist("extend_history",$clean_where))
			{
				$this->db->set('clean', '1');
				$this->db->where($clean_where);
				$this->db->update('extend_history');

			}
			else $this->basic->insert_data("extend_history",$insert_data);
			
			$this->session->set_flashdata('success_message', 1);
			$this->clean($id);
		}
    }
	
	public function set_a_action($id)
    {				
		//clean usage logs action
		$table_clean = 'users';
		$where_clean =  array(
		'id' => $id,
		);

		$data=array
		 (
			 "vat_no"=>"A",
		 );

		$this->basic->update_data($table_clean, $where_clean, $data);
		//index();
		$data['body'] = 'agents/clean';
        $data['page_title'] =  $this->lang->line("A");
        $this->_viewcontroller($data);
    }	

	public function set_b_action($id)
    {				
		//clean usage logs action
		$table_clean = 'users';
		$where_clean =  array(
		'id' => $id,
		);

		$data=array
		 (
			 "vat_no"=>"B",
		 );

		$this->basic->update_data($table_clean, $where_clean, $data);
		//index();
		$data['body'] = 'agents/clean';
        $data['page_title'] =  $this->lang->line("B");
        $this->_viewcontroller($data);
    }	
	
	public function set_x_action($id)
    {				
		//clean usage logs action
		$table_clean = 'users';
		$where_clean =  array(
		'id' => $id,
		);

		$data=array
		 (
			 "vat_no"=>"X",
		 );

		$this->basic->update_data($table_clean, $where_clean, $data);
		//index();
		$data['body'] = 'agents/clean';
        $data['page_title'] =  $this->lang->line("X");
        $this->_viewcontroller($data);
    }		
	
	public function set_w_action($id)
    {				
		//clean usage logs action
		$table_clean = 'users';
		$where_clean =  array(
		'id' => $id,
		);

		$data=array
		 (
			 "vat_no"=>"W",
		 );

		$this->basic->update_data($table_clean, $where_clean, $data);
		//index();
		$data['body'] = 'agents/clean';
        $data['page_title'] =  $this->lang->line("W");
        $this->_viewcontroller($data);
    }		
	
	public function set_k_action($id)
    {				
		//clean usage logs action
		$table_clean = 'users';
		$where_clean =  array(
		'id' => $id,
		);

		$data=array
		 (
			 "vat_no"=>"K",
		 );

		$this->basic->update_data($table_clean, $where_clean, $data);
		//index();
		$data['body'] = 'agents/clean';
        $data['page_title'] =  $this->lang->line("K");
        $this->_viewcontroller($data);
    }		

	public function set_l_action($id)
    {				
		//clean usage logs action
		$table_clean = 'users';
		$where_clean =  array(
		'id' => $id,
		);

		$data=array
		 (
			 "vat_no"=>"L",
		 );

		$this->basic->update_data($table_clean, $where_clean, $data);
		//index();
		$data['body'] = 'agents/clean';
        $data['page_title'] =  $this->lang->line("L");
        $this->_viewcontroller($data);
    }	
	
    public function days_check($str)
    {
        if (intval($str) > 10)
        {
                $this->form_validation->set_message("days_check", $this->lang->line("days is invalid"));
                return FALSE;
        }
        else
        {
                return TRUE;
        }
    }
}
?>