<?php
$lang['is_exist']			= "<b>%s</b> does not exist"; // User-defind (Created by <PERSON><PERSON><PERSON>)
$lang['required']			= "<b>%s</b> is required";
$lang['isset']				= "<b>%s</b> must have a value";
$lang['valid_email']		= "<b>%s</b> must contain a valid email address";
$lang['valid_emails']		= "<b>%s</b> must contain all valid email addresses";
$lang['valid_url']			= "<b>%s</b> must contain a valid URL";
$lang['valid_ip']			= "<b>%s</b> must contain a valid IP";
$lang['min_length']			= "<b>%s</b> must be at least <b>%s</b> characters in length";
$lang['max_length']			= "<b>%s</b> can not exceed <b>%s</b> characters in length";
$lang['exact_length']		= "<b>%s</b> must be exactly <b>%s</b> characters in length";
$lang['alpha']				= "<b>%s</b> may only contain alphabetical characters";
$lang['alpha_numeric']		= "<b>%s</b> may only contain alpha-numeric characters";
$lang['alpha_dash']			= "<b>%s</b> may only contain alpha-numeric characters, underscores, and dashes";
$lang['numeric']			= "<b>%s</b> must contain only numbers";
$lang['is_numeric']			= "<b>%s</b> must contain only numeric characters";
$lang['integer']			= "<b>%s</b> must contain an integer";
$lang['regex_match']		= "<b>%s</b> is not in correct format";
$lang['matches']			= "<b>%s</b> does not match <b>%s</b> field";
$lang['is_unique'] 			= "<b>%s</b> is already used";
$lang['is_natural']			= "<b>%s</b> must contain only positive numbers";
$lang['is_natural_no_zero']	= "<b>%s</b> must contain a number greater than zero";
$lang['decimal']			= "<b>%s</b> must contain a decimal number";
$lang['less_than']			= "<b>%s</b> must contain a number less than <b>%s</b>";
$lang['greater_than']		= "<b>%s</b> must contain a number greater than <b>%s</b>";


/* End of file form_validation_lang.php */
/* Location: ./system/language/english/form_validation_lang.php */