<?php
require_once("Home.php"); // including home controller
set_time_limit(0);
 
class Persist extends CI_Controller 
{
	public function __construct()
    {
        parent::__construct();
	}
		public function remove($id)
		{
			echo "user id : $id<br>";
			$this->load->database();
			//$query = $this->db->query('SELECT id,page_id,page_name,page_access_token FROM facebook_rx_fb_page_info WHERE id IN (SELECT MAX(id) FROM facebook_rx_fb_page_info GROUP BY page_id) AND id ='. $id);
			$query = $this->db->query('SELECT id,page_id,page_name,page_access_token FROM facebook_rx_fb_page_info WHERE id ='. $id);
			$this->load->library("messenger_bot_login");
			$idx = 1;
			$rec_count = $query->num_rows();
			echo "record count : $rec_count<br>";
			foreach ($query->result() as $row)
			{
				echo $idx . "/" . $rec_count . "<br>";
				echo $row->id ." | ". $row->page_id ." | ". $row->page_name . " | " . $row->page_access_token . "<br>" ;
		
				$page_access_token = $row->page_access_token;
				try
				{ 
					$res1 = $this->messenger_bot_login->delete_get_started_button($page_access_token);
					echo "res1: <br>";
					print_r($res1);
					sleep (1);
					
					$res2 = $this->messenger_bot_login->delete_persistent_menu($page_access_token);
					echo "res2: <br>";
					print_r($res2);
					sleep (1);
					
					$this->messenger_bot_login->unset_welcome_message($page_access_token);
					sleep (1);
					
					//$this->messenger_bot_login->add_get_started_button($page_access_token);
					//sleep (1);
					//$this->messenger_bot_login->set_welcome_message($page_access_token,"مرحباً, أنا المجيب التلقائي شهير");
					//sleep (1);
				
					//$json_array=array();
				
					//$copyright_text = "خدمة شهير للرد التلقائي"; 
					//$copyright_url = "https://www.shaheerbot.net";
					//$temp["locale"] = "default";
					//$temp["composer_input_disabled"] = false;
				
					//$temp["call_to_actions"][0]["title"]=$copyright_text;
					//$temp["call_to_actions"][0]["type"]="web_url";
					//$temp["call_to_actions"][0]["url"]=$copyright_url;
		
				//$trial_text = "شهير للرد التلقائي";
				//$trial_url = "https://www.shaheer.ga";
				
				//$temp["call_to_actions"][1]["title"]=$trial_text;
				//$temp["call_to_actions"][1]["type"]="web_url";
				//$temp["call_to_actions"][1]["url"]=$trial_url;
				
					//$json_array["persistent_menu"][]=$temp;
				
					//$json=json_encode($json_array);
				
				// echo $json;
				// echo "<br>";
				 
					//$response=$this->messenger_bot_login->add_persistent_menu($page_access_token,$json);
				//print_r( $response);
					echo '<p style="color:green;">Done</p>';
					sleep (1);
				}
				catch (Exception $e)
				{
				    //echo 'Caught exception: ',  $e->getMessage(), "\n";
					echo '<p style="color:red;">'.$e->getMessage().'</p>';
				}
				// echo "Response <br>";
				// print_r ($response);
				$idx++;
			}
			echo "<b> Persistant Menu Removed! </b>"; 
		}
		
		public function apply($id) 
		{
			echo "user id : $id<br>";
			
			
			$this->load->database();
			
		$query = $this->db->query('SELECT id,page_id,page_name,page_access_token FROM facebook_rx_fb_page_info WHERE id IN (SELECT MAX(id) FROM facebook_rx_fb_page_info GROUP BY page_id) AND id ='. $id);

		$this->load->library("messenger_bot_login");
		$idx = 1;
		$rec_count = $query->num_rows();
		echo "record count : $rec_count<br>";
		//die();
		foreach ($query->result() as $row)
		{
			echo $idx . "/" . $rec_count . "<br>";
			echo $row->id ." | ". $row->page_id ." | ". $row->page_name . " | " . $row->page_access_token . "<br>" ;
		
			$page_access_token = $row->page_access_token;
			try
			{ 
				$this->messenger_bot_login->delete_get_started_button($page_access_token);
				sleep (1);
				$this->messenger_bot_login->delete_persistent_menu($page_access_token);
				sleep (1);
				$this->messenger_bot_login->add_get_started_button($page_access_token);
				sleep (1);
				$this->messenger_bot_login->set_welcome_message($page_access_token,"مرحباً, أنا المجيب التلقائي شهير");
				sleep (1);
				
				$json_array=array();
				
				$copyright_text = "خدمة شهير للرد التلقائي"; 
				$copyright_url = "https://www.shaheer.app";
				$temp["locale"] = "default";
				$temp["composer_input_disabled"] = false;
				
				$temp["call_to_actions"][0]["title"]=$copyright_text;
				$temp["call_to_actions"][0]["type"]="web_url";
				$temp["call_to_actions"][0]["url"]=$copyright_url;
		
				//$trial_text = "شهير للرد التلقائي";
				//$trial_url = "https://www.shaheer.ga";
				
				//$temp["call_to_actions"][1]["title"]=$trial_text;
				//$temp["call_to_actions"][1]["type"]="web_url";
				//$temp["call_to_actions"][1]["url"]=$trial_url;
				
				$json_array["persistent_menu"][]=$temp;
				
				$json=json_encode($json_array);
				
				// echo $json;
				// echo "<br>";
				 
				$response=$this->messenger_bot_login->add_persistent_menu($page_access_token,$json);
				//print_r( $response);
				echo '<p style="color:green;">Done</p>';
				sleep (1);
			}
			catch (Exception $e)
			{
				    //echo 'Caught exception: ',  $e->getMessage(), "\n";
					echo '<p style="color:red;">'.$e->getMessage().'</p>';
			}
			// echo "Response <br>";
			// print_r ($response);
			$idx++;
		}
		echo "<b> Persistant Menu Published! </b>"; 
		}
	public function index() 
	{
		
		//$data['body'] = 'Persistant Menu';
		//$this->_viewcontroller($data);
		
		// $this->laod->view('test_template');
		//$page_access_token = "EAAboYFl2IZCEBAMS6BXEc61x3aZBB39KWECjNFZBLSRBviWSvogW4hy9ieWKbZCHvWXvZCcXtZBFUnw0ybwOnE1LZCaEgWOZCIl5DSz8ZAutZCO1PFJHAgfHZB7XzF5xMyEl8TtPuB7WUXukAPXWZByRK4UaHcbMtZClggKscCz5AaEZADnQZDZD";
		//$page_access_token = "EAAboYFl2IZCEBAErkr3dcWUOaGXMHgUZA8o2D8RNeZB88NxlTDWbDaxZBuPQapiZC2fTBPPQJqE8rwfbg4qbf6YVTb4X24mgfOvVrTq0CZCwdpXmjfQLUS2Widz1ZABiHOPuKe2J8fZCVUcKuZBlIQ9hZB03AvlTPBjrGcDvhXjsRIMwZDZD";
		$this->load->database();
		$query = $this->db->query('SELECT id,page_id,page_name,page_access_token FROM facebook_rx_fb_page_info WHERE id IN (SELECT MAX(id) FROM facebook_rx_fb_page_info GROUP BY page_id) AND id >= 626 ');

		$this->load->library("messenger_bot_login");
		$idx = 1;
		$rec_count = $query->num_rows();
		foreach ($query->result() as $row)
		{
			echo $idx . "/" . $rec_count . "<br>";
			echo $row->id ." | ". $row->page_id ." | ". $row->page_name . " | " . $row->page_access_token . "<br>" ;
		
			$page_access_token = $row->page_access_token;
			try
			{ 
				$this->messenger_bot_login->delete_get_started_button($page_access_token);
				sleep (10);
				$this->messenger_bot_login->delete_persistent_menu($page_access_token);
				sleep (10);
				$this->messenger_bot_login->add_get_started_button($page_access_token);
				sleep (10);
				$this->messenger_bot_login->set_welcome_message($page_access_token,"مرحباً, أنا المجيب التلقائي شهير");
				sleep (10);
				
				$json_array=array();
				
				$copyright_text = "خدمة شهير للرد التلقائي"; 
				$copyright_url = "https://www.shaheerbot.net";
				$temp["locale"] = "default";
				$temp["composer_input_disabled"] = false;
				
				$temp["call_to_actions"][0]["title"]=$copyright_text;
				$temp["call_to_actions"][0]["type"]="web_url";
				$temp["call_to_actions"][0]["url"]=$copyright_url;
		
				//$trial_text = "شهير للرد التلقائي";
				//$trial_url = "https://www.shaheer.ga";
				
				//$temp["call_to_actions"][1]["title"]=$trial_text;
				//$temp["call_to_actions"][1]["type"]="web_url";
				//$temp["call_to_actions"][1]["url"]=$trial_url;
				
				$json_array["persistent_menu"][]=$temp;
				
				$json=json_encode($json_array);
				
				// echo $json;
				// echo "<br>";
				 
				$response=$this->messenger_bot_login->add_persistent_menu($page_access_token,$json);
				//print_r( $response);
				echo '<p style="color:green;">Done</p>';
				sleep (10);
			}
			catch (Exception $e)
			{
				    //echo 'Caught exception: ',  $e->getMessage(), "\n";
					echo '<p style="color:red;">'.$e->getMessage().'</p>';
			}
			// echo "Response <br>";
			// print_r ($response);
			$idx++;
		}
		echo "<b> Persistant Menu Published! </b>"; 
	}
}
?>