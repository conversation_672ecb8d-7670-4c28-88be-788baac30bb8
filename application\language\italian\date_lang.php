<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * <AUTHOR>
 * @copyright	Copyright (c) 2014 - 2016, British Columbia Institute of Technology (http://bcit.ca/)
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	http://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['date_year'] = 'Anno';
$lang['date_years'] = 'Anni';
$lang['date_month'] = 'Mese';
$lang['date_months'] = 'Mesi';
$lang['date_week'] = 'Settimana';
$lang['date_weeks'] = 'Settimane';
$lang['date_day'] = 'Giorno';
$lang['date_days'] = 'Giorni';
$lang['date_hour'] = 'Ora';
$lang['date_hours'] = 'Ore';
$lang['date_minute'] = 'Minuto';
$lang['date_minutes'] = 'Minuti';
$lang['date_second'] = 'Secondo';
$lang['date_seconds'] = 'Secondi';

$lang['UM12']	 = '(UTC -12:00) Baker/Howland Island';
$lang['UM11']	 = '(UTC -11:00) Niue';
$lang['UM10']	 = '(UTC -10:00) Hawaii-Aleutian Standard Time, Cook Islands, Tahiti';
$lang['UM95']	 = '(UTC -9:30) Marquesas Islands';
$lang['UM9']	 = '(UTC -9:00) Alaska Standard Time, Gambier Islands';
$lang['UM8']	 = '(UTC -8:00) Pacific Standard Time, Clipperton Island';
$lang['UM7']	 = '(UTC -7:00) Mountain Standard Time';
$lang['UM6']	 = '(UTC -6:00) Central Standard Time';
$lang['UM5']	 = '(UTC -5:00) Eastern Standard Time, Western Caribbean Standard Time';
$lang['UM45']	 = '(UTC -4:30) Venezuelan Standard Time';
$lang['UM4']	 = '(UTC -4:00) Atlantic Standard Time, Eastern Caribbean Standard Time';
$lang['UM35']	 = '(UTC -3:30) Newfoundland Standard Time';
$lang['UM3']	 = '(UTC -3:00) Argentina, Brazil, French Guiana, Uruguay';
$lang['UM2']	 = '(UTC -2:00) South Georgia/South Sandwich Islands';
$lang['UM1']	 = '(UTC -1:00) Azores, Cape Verde Islands';
$lang['UTC']	 = '(UTC) Greenwich Mean Time, Western European Time';
$lang['UP1']	 = '(UTC +1:00) Central European Time, West Africa Time';
$lang['UP2']	 = '(UTC +2:00) Central Africa Time, Eastern European Time, Kaliningrad Time';
$lang['UP3']	 = '(UTC +3:00) Moscow Time, East Africa Time, Arabia Standard Time';
$lang['UP35']	 = '(UTC +3:30) Iran Standard Time';
$lang['UP4']	 = '(UTC +4:00) Azerbaijan Standard Time, Samara Time';
$lang['UP45']	 = '(UTC +4:30) Afghanistan';
$lang['UP5']	 = '(UTC +5:00) Pakistan Standard Time, Yekaterinburg Time';
$lang['UP55']	 = '(UTC +5:30) Indian Standard Time, Sri Lanka Time';
$lang['UP575']	 = '(UTC +5:45) Nepal Time';
$lang['UP6']	 = '(UTC +6:00) Bangladesh Standard Time, Bhutan Time, Omsk Time';
$lang['UP65']	 = '(UTC +6:30) Cocos Islands, Myanmar';
$lang['UP7']	 = '(UTC +7:00) Krasnoyarsk Time, Cambodia, Laos, Thailand, Vietnam';
$lang['UP8']	 = '(UTC +8:00) Australian Western Standard Time, Beijing Time, Irkutsk Time';
$lang['UP875']	 = '(UTC +8:45) Australian Central Western Standard Time';
$lang['UP9']	 = '(UTC +9:00) Japan Standard Time, Korea Standard Time, Yakutsk Time';
$lang['UP95']	 = '(UTC +9:30) Australian Central Standard Time';
$lang['UP10']	 = '(UTC +10:00) Australian Eastern Standard Time, Vladivostok Time';
$lang['UP105']	 = '(UTC +10:30) Lord Howe Island';
$lang['UP11']	 = '(UTC +11:00) Srednekolymsk Time, Solomon Islands, Vanuatu';
$lang['UP115']	 = '(UTC +11:30) Norfolk Island';
$lang['UP12']	 = '(UTC +12:00) Fiji, Gilbert Islands, Kamchatka Time, New Zealand Standard Time';
$lang['UP1275'] = '(UTC +12:45) Chatham Islands Standard Time';
$lang['UP13']	 = '(UTC +13:00) Samoa Time Zone, Phoenix Islands Time, Tonga';
$lang['UP14']	 = '(UTC +14:00) Line Islands';
