<?php
require_once("Home.php"); // including home controller
class Support extends Home
{
	public function __construct()
    {
        parent::__construct();
		
		if ($this->session->userdata('logged_in')!= 1) {
            redirect('home/login', 'location');
        }				
		
        $this->important_feature();
    }
	
	public function index()
    {
		$data['page_title'] = $this->lang->line("Support");
        $data['body'] = 'member/support';
        $this->_viewcontroller($data);		
    }
}
?>