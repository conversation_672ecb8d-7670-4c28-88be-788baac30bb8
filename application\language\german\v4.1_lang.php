<?php 

$lang = array (
  'Cron Job' => 'Cron-Job',
  'do you want to get email alert for unread messages ?' => 'Möchten Sie eine E-Mail-Benachrichtigung für ungelesene Nachrichten erhalten?',
  'this campaign is already enable for processing.' => 'Diese Kampagne ist bereits für die Verarbeitung aktiviert.',
  'you have not enter any domain name or FB page url' => 'Sie haben keinen Domain-Namen oder eine FB-Seiten-URL eingegeben',
  'login with facebook' => 'Einloggen mit Facebook',
  'login with google' => 'Einloggen mit Google',
  'user access token is valid. you can login and get new user access token if you want.' => 'Benutzerzugriffstoken ist gültig. Sie können sich anmelden und neue Benutzerrechte erhalten, wenn Sie möchten.',
  'go back' => 'zurückgehen',
  'tag user' => 'Tag Benutzer',
  'You can tag user in your comment reply. Facebook will notify them about mention whenever you tag.' => '<PERSON>e könne<PERSON> den <PERSON>utz<PERSON> in Ihrer Kommentarantwort markieren. Facebook wird sie über die Erwähnung benachrichtigen, wann immer Sie taggen.',
  'delay used in auto-reply (seconds)' => 'Verzögerung bei der automatischen Antwort (Sekunden)',
  'auto-reply campaign live duration (days)' => 'automatische Antwort-Kampagne Live-Dauer (Tage)',
  'write your message which you want to send. You can customize the message by individual commenter name.' => 'Schreiben Sie Ihre Nachricht, die Sie senden möchten. Sie können die Nachricht individuell nach dem Namen des Kommentators anpassen.',
  'Campaign have been submitted successfully.' => 'Kampagne wurde erfolgreich gesendet.',
  'See report' => 'Siehe Bericht',
);