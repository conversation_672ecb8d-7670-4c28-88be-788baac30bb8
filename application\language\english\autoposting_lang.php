<?php 
	
$lang = array(
"Auto Posting" => "Auto Posting",
"Auto Posting Settings" => "Auto Posting Settings",
"Auto Posting Feed" => "Auto Posting Feed",
"New Auto Posting Feed" => "New Auto Posting Feed",
"No settings found." => "No settings found.",
"Feed List" => "Feed List",
"Do you really want to delete this settings?" => "Do you really want to delete this settings?",
"Do you really want to disable this settings?" => "Do you really want to disable this settings?",
"Do you really want to enable this settings? This operation may take few time." => "Do you really want to enable this settings? This operation may take few time.",
"Add Feed" => "Add Feed",
"Feed Name" => "Feed Name",
"RSS Feed URL" => "RSS Feed URL",
"Feed URL can not be empty." => "Feed URL can not be empty.",
"Please select feed type." => "Please select feed type.",
"Please provide a feed name." => "Please provide a feed name.",
"Page Names" => "Page Names",
"Feed Type" => "Feed Type",
"RSS feed has been added successfully." => "RSS feed has been added successfully.",
"This feed URL has been already added." => "This feed URL has been already added.",
"settings has been stored to database successfully." => "Settings has been stored to database successfully.",
"settings has been failed to store in database." => "Settings has been failed to store in database.",
"Campaign Settings" => "Campaign Settings",
"Post as" => "Post as",
"You must specify the last publication date." => "You must specify the last publication date.",
"Post Start Time" => "Post Start Time",
"Post End Time" => "Post End Time",
"Broadcast to Pages" => "Broadcast to Pages",
"Notification Type" => "Notification Type",
"Choose Labels" => "Choose Labels",
"Exclude Label" => "Exclude Label",
"(keep blank to send to all)" => "(keep blank to send to all)",
"Post to Pages" => "Post to Pages",
"Feed not found." => "Feed not found.",
"Please select pages to publish the new feeds as Facebook post or select a page to broadcast new the feeds to messenger subscribers." => "Please select pages to publish the new feeds as Facebook post or select a page to broadcast new the feeds to messenger subscribers.",
"Sorry, you are not permitted to post this amount in bulk, posting bulk limit has been exceeded." => "Sorry, you are not permitted to post this amount in bulk, posting bulk limit has been exceeded.",
"click here to see usage log" => "Click here to see usage log",
"Sorry, you are not permitted to post more to Facebook this month, posting monthly limit has been exceeded." => "Sorry, you are not permitted to post more to Facebook this month, posting monthly limit has been exceeded.",
"Sorry, you are not permitted to create more quick bulk broadcast campaign this month, posting monthly limit has been exceeded." => "Sorry, you are not permitted to create more quick bulk broadcast campaign this month, posting monthly limit has been exceeded.",
"Campaign has been submitted successfully and will start processing shortly as per your settings." => "Campaign has been submitted successfully and will start processing shortly as per your settings.",
"Broadcast Start Time" => "Broadcast Start Time",
"Broadcast End Time" => "Broadcast End Time",
"Quick Broadcast Campaign Setup" => "Quick Broadcast Campaign Setup",
"Facebook Posting Campaign Setup" => "Facebook Posting Campaign Setup",
"Post Between Time" => "Post Between Time",
"Broadcast Between Time" => "Broadcast Between Time",
"To" => "to",
"Broadcast Timezone" => "Broadcast Timezone",
"Posting Timezone" => "Posting Timezone",
"Submit Campaign" => "Submit Campaign",
"Post as Pages" => "Post as Pages",
"Broadcast as Page" => "Broadcast as Page",
"Last Updated" => "Last Updated",
"Last Feed" => "Last Feed",
"Please select post between times." => "Please select post between times.",
"Post between start time must be less than end time and need to have minimum one hour time span." => "Post between start time must be less than end time and need to have minimum one hour time span.",
"Please select broadcast between times." => "Please select broadcast between times.",
"Broadcast between start time must be less than end time and need to have minimum one hour time span." => "Broadcast between start time must be less than end time and need to have minimum one hour time span.",
"Access forbidden : you do not have access to publish/broadcast module. Please contact application admin to get access." => "Access forbidden : you do not have access to publish/broadcast module. Please contact application admin to get access.",
"something went wrong while fetching feed data." => "Something went wrong while fetching feed data.",
"something went wrong while creating broadcast campaign." => "Something went wrong while creating broadcast campaign.",
"Your monthly limit for Facebook posting module has been exceeded." => "Your monthly limit for Facebook posting module has been exceeded.",
"RSS Auto Posting [once per 5 minute or higher]" => "RSS Auto Posting [once per 5 minute or higher]",
"Your monthly limit for quick bulk broadcasting module has been exceeded." => "Your monthly limit for quick bulk broadcasting module has been exceeded.",
"Invalid API key." => "Invalid API key.",
"paused" => "Paused",
"If the system gets small number of feeds they will be processed in first hour of given time range. If system gets large amount of feeds then they will be processed spanning all over the time range." => "If the system gets small number of feeds they will be processed in first hour of given time range. If system gets large amount of feeds then they will be processed spanning all over the time range.",
"Regular Push notification will make a sound and display a phone notification. Use it for important messages." => "Regular Push notification will make a sound and display a phone notification. Use it for important messages.",
"Silent Push notification will display a phone notification without sound. Use it for regular messages that do not require immediate action." => "Silent Push notification will display a phone notification without sound. Use it for regular messages that do not require immediate action.",
"No push will not display any notification. Use it for silently sending content updates." => "No push will not display any notification. Use it for silently sending content updates.",
"Display Unsubscribe Button?" => "Display Unsubscribe Button?",
);
?>