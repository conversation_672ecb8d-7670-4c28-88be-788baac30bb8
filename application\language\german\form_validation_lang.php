<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * @copyright	Copyright (c) 2014 - 2016, British Columbia Institute of Technology (http://bcit.ca/)
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	http://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');
$lang['is_exist']			= "এই <b>%s</b> nicht vorhanden"; // User-defind (Created by Alamin)
$lang['required']		= 'Das <b>%s</b> Formularfeld ist eine Pflichtangabe.';
$lang['isset']			= 'Das <b>%s</b> Formularfeld mus einen gültigen Wert enthalten.';
$lang['valid_email']		= 'Das <b>%s</b> Formularfeld muss eine gültige E-Mail-Adresse beinhalten.';
$lang['valid_emails']		= 'Das <b>%s</b> Formularfeld muss gültige E-Mail-Adressen beinhalten.';
$lang['valid_url']		= 'Das <b>%s</b> Formularfeld muss eine gültige URL beinhalten.';
$lang['valid_ip']		= 'Das <b>%s</b> Formularfeld muss eine gültige IP-Adresse beinhalten.';
$lang['min_length']		= 'Das <b>%s</b> Formularfeld muss mindestens <b>%s</b> Zeichen lang sein.';
$lang['max_length']		= 'Das <b>%s</b> Formularfeld darf nicht mehr als <b>%s</b> Zeichen lang sein.';
$lang['exact_length']		= 'Das <b>%s</b> Formularfeld muss exakt <b>%s</b> Zeichen lang sein.';
$lang['alpha']			= 'Das <b>%s</b> Formularfeld darf nur alphabetische Zeichen enthalten.';
$lang['alpha_numeric']		= 'Das <b>%s</b> Formularfeld darf nur alphanumerische Zeichen enthalten.';
$lang['alpha_numeric_spaces']	= 'Das <b>%s</b> Formularfeld darf nur alphanumerische Zeichen und Leerzeichen enthalten.';
$lang['alpha_dash']		= 'Das <b>%s</b> Formularfeld darf nur alphanumerische Zeichen, Unterstriche und Bindestriche enthalten.';
$lang['numeric']		= 'Das <b>%s</b> Formularfeld darf nur Zahlen enthalten.';
$lang['is_numeric']		= 'Das <b>%s</b> Formularfeld darf nur numerische Zeichen enthalten.';
$lang['integer']		= 'Das <b>%s</b> Formularfeld muss einen Integer-Wert enthalten.';
$lang['regex_match']		= 'Das <b>%s</b> Formularfeld hat keinen gültigen Wert.';
$lang['matches']		= 'Das <b>%s</b> Formularfeld stimmt nicht mit dem <b>%s</b> Formularfeld überein.';
$lang['differs']		= 'Das <b>%s</b> Formularfeld muss sich vom <b>%s</b> Formularfeld unterscheiden.';
$lang['is_unique'] 		= 'Das <b>%s</b> Formularfeld muss einen eindeutigen (unique) Wert enthalten.';
$lang['is_natural']		= 'Das <b>%s</b> Formularfeld muss Zahlen enthalten.';
$lang['is_natural_no_zero']	= 'Das <b>%s</b> Formularfeld muss Zahlen enthalten und größer als Null sein.';
$lang['decimal']		= 'Das <b>%s</b> Formularfeld mus eine Dezimalzahl enthalten.';
$lang['less_than']		= 'Das <b>%s</b> Formularfeld muss einen Zahlenwert enthalten der kleiner als <b>%s</b> ist.';
$lang['less_than_equal_to']	= 'Das <b>%s</b> Formularfeld muss einen Zahlenwert enthalten der kleiner oder gleich <b>%s</b> ist.';
$lang['greater_than']		= 'Das <b>%s</b> Formularfeld muss einen Zahlenwert enthalten der gößer als <b>%s</b> ist.';
$lang['greater_than_equal_to']	= 'Das <b>%s</b> Formularfeld muss einen Wert enthalten, der größer oder gleich <b>%s</b> ist.';
$lang['error_message_not_set']	= 'Kann eine Fehlermeldung entsprechend Ihrer Feldname {field } zugreifen .';
$lang['in_list']		= 'Das <b>%s</b> Formularfeld muss eins dieser Elemente enthalten: <b>%s</b>.';
