<?php 


$lang = array (
  'facebook lead' => 'Facebook Lead',
  'bulk message campaign' => 'Bulk Message Campaign',
  'add-on' => 'Add-on',
  'add-on purchase code' => 'Add-on Purchase Code',
  'add-ons' => 'Add-ons',
  'add-on name' => 'Add-on Name',
  'unique name' => 'Unique Name',
  'installed at' => 'Installed at',
  'install new add-on' => 'Install New Add-on',
  'activate' => 'Activate',
  'deactivate' => 'Deactivate',
  'author' => 'Author',
  'upload' => 'Upload',
  'put the add-on purchase code here to activate. keep it blank and submit if the add-on is free' => 'Put the add-on purchase code here to activate. Keep it blank and submit if the add-on is free.',
  'keep it blank for free add-on' => 'Keep it blank for free add-on',
  'add-on controller has not been provided.' => 'Add-on controller has not been provided.',
  'add-on controller not found.' => 'Add-on controller not found.',
  'add-on unique name has not been provided.' => 'Add-on unique name has not been provided.',
  'add-on is already active. duplicate unique name found.' => 'Add-on is already active. Duplicate unique name found.',
  'add-on is already active. duplicate module id found.' => 'Add-on is already active. Duplicate module id found.',
  'database error. something went wrong.' => 'Database error. Something went wrong.',
  'add-on module ID must be integer.' => 'Add-on module ID must be integer.',
  'are you sure that you want to deactive this add-on?' => 'Are you sure that you want to deactive this add-on?',
  'your add-on data will still remain.' => 'Your add-on data will still remain.',
  'add-on purchase code has not been provided.' => 'Add-on purchase code has not been provided.',
  'something went wrong. CURL is not working.' => 'Something went wrong. CURL is not working.',
  'purchase code not valid or already used.' => 'Purchase code not valid or already used.',
  'are you sure that you want to delete this add-on?' => 'Are you sure that you want to delete this add-on?',
  'this process can not be undone.' => 'This process can not be undone.',
  'add-on has been deleted successfully.' => 'Add-on has been deleted successfully.',
  'add-on has been deactivated successfully.' => 'Add-on has been deactivated successfully.',
  'upload new add-on' => 'Upload New Add-on',
  'browse add-on zip file' => 'Browse Add-on Zip File',
  'after you upload add-on file you will be taken to add-on activation page, you need to active the add-on there.' => 'After you upload add-on file you will be taken to add-on activation page, you need to active the add-on there.',
  'if you are having trouble in file upload with our uploader then you can simply uplaod add-on zip file to application/modules folder, unzip itand activate it from add-on list.'=>'If you are having trouble in file upload with our uploader then you can simply uplaod add-on zip file to application/modules folder, unzip itand activate it from add-on list.',
  'add-on has been uploaded successfully. you can activate it from here.' => 'Add-on has been uploaded successfully. You can activate it from here.',
  'reprocess this campaign' => 'Reprocess this campaign',
  'force process' => 'Force Process',
  'force reprocessing' => 'Force Reprocessing',
  "Force Reprocessing means you are going to process this campaign again from where it ended. You should do only if you think the campaign is hung for long time and didn't send message for long time. It may happen for any server timeout issue or server going down during last attempt or any other server issue. So only click OK if you think message is not sending. Are you sure to Reprocessing ?"=>"Force Reprocessing means you are going to process this campaign again from where it ended. You should do only if you think the campaign is hung for long time and didn't send message for long time. It may happen for any server timeout issue or server going down during last attempt or any other server issue. So only click OK if you think message is not sending. Are you sure to Reprocessing ?",
  'check update' => 'Check Update',
  'update system' => 'Update System',
  'updates' => 'Updates',
  'your current version is' => 'Your current version is',
  'there are follwoing updates avaibale for you:' => 'There are follwoing updates avaibale for you:',
  'product' => 'Product',
  'version' => 'Version',
  'update log' => 'Update Log',
  'see log' => 'See Log',
  'files' => 'Files',
  'SQL' => 'SQL',
  'no update available for you, you are already using lastest version.' => 'No update available for you, you are already using latest version.',
  'system update' => 'System Update',
  'do not close this window or refresh page untill update done.' => 'Do not close this window or refresh page untill update done.',
  'app has been updated successfully.' => 'App has been updated successfully.',
  'your account has been imported successfully.' => 'Your account has been imported successfully.',
  'facebook API settings' => 'Facebook API Settings',
  'what do you want about offensive comments?' => 'What do you want about offensive comments?',
  'hide' => 'Hide',
  'write down the offensive keywords in comma separated' => 'Write down the offensive keywords in comma separated',
  'offensive keywords' => 'Offensive Keywords',
  'write your' => 'Write your',
  'Type keywords here in comma separated (keyword1,keyword2)...Keep it blank for no actions' => 'Type keywords here in comma separated (keyword1,keyword2)...Keep it blank for no actions',
  'private reply message after deleting offensive comment' => 'Private reply message after deleting offensive comment',
  'Type your message here...Keep it blank for no actions' => 'Type your message here...Keep it blank for no actions',
  'do you want to hide comments after comment reply?' => 'Do you want to hide comments after comment reply?',
  'do you want to get  email alert for unread messages ?'=>'Do you want to get  email alert for unread messages ?',
  'image for comment reply' => 'Image for comment reply',
  'put your image url here or click the above upload button' => 'Put your image url here or click the above upload button',
  'video for comment reply' => 'Video for comment reply',
  'video upload' => 'Video upload',
  'Image and video will not work together. Please choose either image or video.' => 'Image and video will not work together. Please choose either image or video.',
  'put your image url here or click upload' => 'Put your image url here or click upload',
  'your browser does not support the video tag.' => 'Your browser does not support the video tag.',
  'theme' => 'Theme',
  'display landing page' => 'Display landing page',
  'your login validity has been expired.' => 'Your login validity has been expired.',
  'last 20 conversations' => 'Last 20 conversations',
  'reply' => 'Reply',
  'type your reply message here' => 'Type your reply message here',
  'generate API key' => 'Generate API key',
  're-generate API key' => 'Re-generate API key',
  'get your API key' => 'Get your API key',
  'your API key' => 'Your API key',
  'Group Name'=>'Group Name',
  'Group ID'=>'Group ID',
  'mkdir() function is not working!'=>'mkdir() function is not working in your server! Please see log and update manually.',
  'mkdir() function is not working!'=>'ZipArchive is not working in your server! Please see log and update manually.',
  'Connection failed to establish, cURL is not working! Visit item description page in codecanyon, see change log and update manually.'=>'Connection failed to establish, cURL is not working! Please see log and update manually.',
  'you have to delete this account.'=>'You have to delete this account.',
  'due to system configuration change you have to delete one or more imported FB accounts and import again. Please check the following accounts and delete the account that has warning to delete.'=>'Due to system configuration change you have to delete one or more imported FB accounts and import again. Please check the following accounts and delete the account that has warning to delete.'
);