<?php
$lang['is_exist']			= "<b>%s</b> does not exist"; // User-defind (Created by <PERSON><PERSON><PERSON>)
$lang['required']			= "<b>%s</b> gerekli";
$lang['isset']				= "<b>%s</b> değ<PERSON> içermeli";
$lang['valid_email']		= "<b>%s</b> geçerli bir e-posta adresi olmalı";
$lang['valid_emails']		= "<b>%s</b> tüm geçerli e-posta adreslerini içermelidir";
$lang['valid_url']			= "<b>%s</b> geçerli bir URL içermelidir";
$lang['valid_ip']			= "<b>%s</b> Geçerli bir IP içermelidir";
$lang['min_length']			= "<b>%s</b> en az <b>%s</b> karakter uzunluğunda olmalı";
$lang['max_length']			= "<b>%s</b> karakter uzunluğu <b>%s</b> aşılmamalı";
$lang['exact_length']		= "<b>%s</b> tam bu uzunlukta <b>%s</b> olmalı";
$lang['alpha']				= "<b>%s</b> Sadece alfabetik karakter içerebilir";
$lang['alpha_numeric']		= "<b>%s</b> sadece alfanümerik karakter içerebilir";
$lang['alpha_dash']			= "<b>%s</b> sadece alfanümerik karakterler, alt ve çizgi içerebilir";
$lang['numeric']			= "<b>%s</b> Sadece sayı içermelidir";
$lang['is_numeric']			= "<b>%s</b> Sadece sayısal karakterler içermelidir";
$lang['integer']			= "<b>%s</b> bir tamsayı içermelidir";
$lang['regex_match']		= "<b>%s</b> Doğru biçimde değil";
$lang['matches']			= "<b>%s</b> alan <b>%s</b> eşleşmiyor";
$lang['is_unique'] 			= "<b>%s</b> zaten kullanılıyor";
$lang['is_natural']			= "<b>%s</b> Sadece pozitif sayılar içermelidir";
$lang['is_natural_no_zero']	= "<b>%s</b> sıfırdan büyük bir sayı içermelidir";
$lang['decimal']			= "<b>%s</b> ondalık sayı içermelidir";
$lang['less_than']			= "<b>%s</b> en az <b>%s</b> den az sayı içermeli";
$lang['greater_than']		= "<b>%s</b> büyük bir sayı içermelidir <b>%s</b>";


/* End of file form_validation_lang.php */
/* Location: ./system/language/english/form_validation_lang.php */