<?php 


$lang = array (
  'facebook lead' => 'facebook kurşun',
  'bulk message campaign' => 'toplu mesaj kampanyası',
  'add-on' => 'Ayriyeten',
  'add-on purchase code' => 'eklenti satın alma kodu',
  'add-ons' => 'Eklentiler',
  'add-on name' => 'eklenti adı',
  'unique name' => 'benzersiz adı',
  'installed at' => 'yüklü',
  'install new add-on' => 'yeni eklenti kur',
  'activate' => 'etkinleştirmek',
  'deactivate' => 'devre dışı bırakmak',
  'author' => 'yazar',
  'upload' => 'yüklemek',
  'put the add-on purchase code here to activate. keep it blank and submit if the add-on is free' => 'etkinleştirmek için eklenti satın alma kodunu buraya koyun. boş bırakın ve eklenti ücretsiz ise gönderin',
  'keep it blank for free add-on' => 'ücretsiz eklenti için boş bı<PERSON>',
  'add-on controller has not been provided.' => 'eklenti denetleyicisi sağlanmadı.',
  'add-on controller not found.' => 'eklenti denetleyicisi bulunamadı.',
  'add-on unique name has not been provided.' => 'eklenti benzersiz adı sağlanmadı.',
  'add-on is already active. duplicate unique name found.' => 'eklenti zaten etkin. yinelenen benzersiz adı bulundu.',
  'add-on is already active. duplicate module id found.' => 'eklenti zaten etkin. yinelenen modül kimliği bulundu.',
  'database error. something went wrong.' => 'veri tabanı hatası. bir şeyler yanlış gitti.',
  'add-on module ID must be integer.' => 'Eklenti modülü kimliği tam sayı olmalıdır.',
  'are you sure that you want to deactive this add-on?' => 'bu eklentiyi devre dışı bırakmak istediğinizden emin misiniz?',
  'your add-on data will still remain.' => 'eklenti verileriniz hala kalacaktır.',
  'add-on purchase code has not been provided.' => 'eklenti satın alma kodu sağlanmadı.',
  'something went wrong. CURL is not working.' => 'bir şeyler yanlış gitti. CURL çalışmıyor.',
  'purchase code not valid or already used.' => 'satın alma kodu geçerli değil veya zaten kullanılmış.',
  'are you sure that you want to delete this add-on?' => 'bu eklentiyi silmek istediğinizden emin misiniz?',
  'this process can not be undone.' => 'bu işlem geri alınamaz.',
  'add-on has been deleted successfully.' => 'eklenti başarıyla silindi.',
  'add-on has been deactivated successfully.' => 'eklenti başarıyla devre dışı bırakıldı.',
  'upload new add-on' => 'yeni eklenti yükle',
  'browse add-on zip file' => 'tarama eklenti zip dosyası',
  'after you upload add-on file you will be taken to add-on activation page, you need to active the add-on there.' => 'Eklenti dosyasını yükledikten sonra, eklenti etkinleştirme sayfasına yönlendirileceksiniz, burada eklentiyi etkinleştirmeniz gerekiyor.',
  'if you are having trouble in file upload with our uploader then you can simply uplaod add-on zip file to application/modules folder, unzip itand activate it from add-on list.' => 'uploader ile dosya yükleme konusunda sorun yaşıyorsanız, add-on zip dosyasını application / modules klasörüne yükseltin, unzip açın ve eklenti listesinden etkinleştirin.',
  'add-on has been uploaded successfully. you can activate it from here.' => 'eklenti başarıyla yüklendi. buradan etkinleştirebilirsiniz.',
  'reprocess this campaign' => 'bu kampanyayı yeniden işle',
  'force process' => 'kuvvet işlemi',
  'force reprocessing' => 'yeniden işleme zorla',
  'Force Reprocessing means you are going to process this campaign again from where it ended. You should do only if you think the campaign is hung for long time and didn`t send message for long time. It may happen for any server timeout issue or server going down during last attempt or any other server issue. So only click OK if you think message is not sending. Are you sure to Reprocessing ?' => 'Zorla Yeniden işleme, bu kampanyayı sona erdikten sonra tekrar işleyeceğiniz anlamına gelir. Yalnızca kampanyanın uzun süre askıda olduğunu ve uzun süre mesaj göndermediğini düşünüyorsanız yapmalısınız. Herhangi bir sunucu zaman aşımı sorunu veya sunucu son girişimi sırasında veya başka bir sunucu sorunu gidiyor oluşabilir. Mesaj gönderilmediğini düşünüyorsanız, yalnızca Tamam`ı tıklayın. Yeniden İşleme`den emin misin?',
  'check update' => 'güncellemeyi kontrol et',
  'update system' => 'güncelleme sistemi',
  'updates' => 'güncellemeler',
  'your current version is' => 'geçerli sürümü',
  'there are follwoing updates avaibale for you:' => 'sizin için şu güncellemeler mevcut:',
  'product' => 'ürün',
  'version' => 'versiyon',
  'update log' => 'günlüğü güncelle',
  'see log' => 'günlüğü gör',
  'files' => 'dosyalar',
  'SQL' => 'SQL',
  'no update available for you, you are already using lastest version.' => 'Kullanabileceğiniz bir güncelleme yok, zaten en son sürümü kullanıyorsunuz.',
  'system update' => 'sistem güncellemesi',
  'do not close this window or refresh page untill update done.' => 'güncelleme bitinceye kadar bu pencereyi kapatmayın veya sayfayı yenileyin.',
  'app has been updated successfully.' => 'uygulama başarıyla güncellendi.',
  'your account has been imported successfully.' => 'hesabınız başarıyla alındı.',
  'facebook API settings' => 'facebook API ayarları',
  'what do you want about offensive comments?' => 'Saldırgan yorumlar hakkında ne istiyorsun?',
  'hide' => 'saklamak',
  'write down the offensive keywords in comma separated' => 'saldırgan anahtar kelimeleri virgülle ayrılmış olarak yaz',
  'offensive keywords' => 'rahatsız edici anahtar kelimeler',
  'write your' => 'yaz',
  'Type keywords here in comma separated (keyword1,keyword2)...Keep it blank for no actions' => 'Buraya anahtar kelimeleri virgülle ayrılmış olarak yazın (keyword1, keyword2) ... İşlem yapılmadığında boş bırakın',
  'private reply message after deleting offensive comment' => 'saldırgan yorum silindikten sonra özel yanıt mesajı',
  'Type your message here...Keep it blank for no actions' => 'Mesajınızı buraya yazın ... Hareketsiz bırakın boş bırakın',
  'do you want to hide comments after comment reply?' => 'Yorum cevabından sonra yorumları gizlemek ister misiniz?',
  'do you want to get email alert for unread messages ?' => 'okunmamış mesajlar için e-posta uyarısı almak istiyor musunuz?',
  'image for comment reply' => 'yorum cevabı için resim',
  'put your image url here or click the above upload button' => 'resim URL`nizi buraya koyun veya yukarıdaki yükleme düğmesini tıklayın',
  'video for comment reply' => 'yorum cevap videosu',
  'video upload' => 'video yükle',
  'Image and video will not work together. Please choose either image or video.' => 'Resim ve video birlikte çalışmaz. Lütfen resim veya video seçin.',
  'put your image url here or click upload' => 'resim URL`nizi buraya koyun veya upload`u tıklayın',
  'your browser does not support the video tag.' => 'tarayıcınız video etiketini desteklemez.',
  'theme' => 'tema',
  'display landing page' => 'açılış sayfasını görüntüle',
  'your login validity has been expired.' => 'oturum açma geçerliliğiniz doldu.',
  'last 20 conversations' => 'son 20 konuşma',
  'reply' => 'cevap',
  'type your reply message here' => 'cevabını buraya yaz',
  'generate API key' => 'API anahtarı oluştur',
  're-generate API key' => 'API anahtarı yeniden üret',
  'get your API key' => 'API anahtarınızı al',
  'your API key' => 'API anahtarınız',
  'Group Name' => 'Grup ismi',
  'Group ID' => 'Grup Kimliği',
  'mkdir() function is not working!' => 'mkdir () işlevi çalışmıyor!',
  'Connection failed to establish, cURL is not working! Visit item description page in codecanyon, see change log and update manually.' => 'Bağlantı kurulamadı, cURL çalışmıyor! Codecanyon`daki öğe açıklama sayfasını ziyaret edin, günlüğü değiştir ve el ile güncelleme bölümüne bakın.',
  'you have to delete this account.' => 'bu hesabı silmek zorundasın.',
  'due to system configuration change you have to delete one or more imported FB accounts and import again. Please check the following accounts and delete the account that has warning to delete.' => 'sistem yapılandırması değişikliği nedeniyle bir veya daha fazla içe aktarılan FB hesabını silmeniz ve tekrar içe aktarmanız gerekir. Lütfen aşağıdaki hesapları kontrol edin ve silmek için uyarısı olan hesabı silin.',
);