<?php 

$lang = array (
  'Cron Job' => 'Cron İşi',
  'do you want to get email alert for unread messages ?' => 'okunmamış mesajlar için e-posta uyarısı almak istiyor musunuz?',
  'this campaign is already enable for processing.' => 'bu kampanya zaten işleme için etkinleştirildi.',
  'you have not enter any domain name or FB page url' => 'herhangi bir alan adı veya FB sayfası URL\'si girmediniz',
  'login with facebook' => 'facebook ile giriş',
  'login with google' => 'google ile giriş',
  'user access token is valid. you can login and get new user access token if you want.' => 'kullanıcı erişim belirteci geçerlidir. İsterseniz oturum açabilir ve yeni kullanıcı erişim izni alabilirsiniz.',
  'go back' => 'geri dön',
  'tag user' => 'kullanıcıyı etiketle',
  'You can tag user in your comment reply. Facebook will notify them about mention whenever you tag.' => 'Yorum cevabında kullanıcı etiketleyebilirsin. Facebook, etiketlediğinizde söz etmesini onlara haberdar edecek.',
  'delay used in auto-reply (seconds)' => 'otomatik cevaplamada kullanılan gecikme (saniye)',
  'auto-reply campaign live duration (days)' => 'otomatik yanıtlama kampanyasının canlı süresi (gün)',
  'write your message which you want to send. You can customize the message by individual commenter name.' => 'göndermek istediğiniz mesajınızı yazın. İletiyi bağımsız yorumcu adıyla özelleştirebilirsiniz.',
  'Campaign have been submitted successfully.' => 'Kampanya başarıyla gönderildi.',
  'See report' => 'Rapora bak',
);