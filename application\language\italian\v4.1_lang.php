<?php 

$lang = array (
  'Cron Job' => 'Cron Job',
  'do you want to get email alert for unread messages ?' => 'vuoi ricevere un avviso via email per i messaggi non letti?',
  'this campaign is already enable for processing.' => 'questa campagna è già abilitata per l\'elaborazione.',
  'you have not enter any domain name or FB page url' => 'non hai inserito alcun nome di dominio o URL della pagina FB',
  'login with facebook' => 'accedi con facebook',
  'login with google' => 'accedi con google',
  'user access token is valid. you can login and get new user access token if you want.' => 'il token di accesso dell\'utente è valido. puoi accedere e ottenere un nuovo token di accesso utente, se lo desideri.',
  'go back' => 'torna indietro',
  'tag user' => 'tag utente',
  'You can tag user in your comment reply. Facebook will notify them about mention whenever you tag.' => 'Puoi taggare l\'utente nella tua risposta di commento. Facebook notificherà loro la menzione ogni volta che tagga.',
  'delay used in auto-reply (seconds)' => 'ritardo utilizzato in risposta automatica (secondi)',
  'auto-reply campaign live duration (days)' => 'campagna di risposta automatica durata live (giorni)',
  'write your message which you want to send. You can customize the message by individual commenter name.' => 'scrivi il tuo messaggio che vuoi inviare. È possibile personalizzare il messaggio in base al nome del singolo commentatore.',
  'Campaign have been submitted successfully.' => 'La campagna è stata inviata con successo.',
  'See report' => 'Vedi rapporto',
);