<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * <AUTHOR>
 * @copyright	Copyright (c) 2014 - 2016, British Columbia Institute of Technology (http://bcit.ca/)
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	http://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['date_year'] = 'Año';
$lang['date_years'] = 'Años';
$lang['date_month'] = 'Mes';
$lang['date_months'] = 'Meses';
$lang['date_week'] = 'Semana';
$lang['date_weeks'] = 'Semanas';
$lang['date_day'] = 'Día';
$lang['date_days'] = 'Días';
$lang['date_hour'] = 'Hora';
$lang['date_hours'] = 'Horas';
$lang['date_minute'] = 'Minuto';
$lang['date_minutes'] = 'Minutos';
$lang['date_second'] = 'Segundo';
$lang['date_seconds'] = 'Segundos';

$lang['UM12']	= '(UTC -12:00) Isla Howland/Baker';
$lang['UM11']	= '(UTC -11:00) Niue';
$lang['UM10']	= '(UTC -10:00) Tiempo de Hawái-Aleutiano, Islas Cook, Tahití';
$lang['UM95']	= '(UTC -9:30) Islas Marquesas';
$lang['UM9']	= '(UTC -9:00) Tiempo de Alaska, Islas Gambier';
$lang['UM8']	= '(UTC -8:00) Tiempo del Pacífico, Isla Clipperton';
$lang['UM7']	= '(UTC -7:00) Tiempo de la montaña';
$lang['UM6']	= '(UTC -6:00) Hora Estándar del Centro';
$lang['UM5']	= '(UTC -5:00) Tiempo del Este, Hora Estándar del Caribe Occidental';
$lang['UM45']	= '(UTC -4:30) Hora Estándar de Venezuela';
$lang['UM4']	= '(UTC -4:00) Tiempo Estándar del Atlántico, Hora Estándar del Caribe Oriental';
$lang['UM35']	= '(UTC -3:30) Tiempo de Terranova y Labrador';
$lang['UM3']	= '(UTC -3:00) Argentina, Brasil, Guayana Francesa, Uruguay';
$lang['UM2']	= '(UTC -2:00) Islas Georgias del Sur/Islas Sandwich del Sur';
$lang['UM1']	= '(UTC -1:00) Azores, Islas de Cabo Verde';
$lang['UTC']	= '(UTC) Tiempo medio de Greenwich, Hora de Europa Occidental';
$lang['UP1']	= '(UTC +1:00) Hora Central Europea, Tiempo de África Occidental';
$lang['UP2']	= '(UTC +2:00) Tiempo de África Central, Hora de Europa Oriental, Hora de Kaliningrado';
$lang['UP3']	= '(UTC +3:00) Hora de Moscú, Tiempo de África Oriental, Hora Estándar de Arabia';
$lang['UP35']	= '(UTC +3:30) Hora Estándar de Irán';
$lang['UP4']	= '(UTC +4:00) Hora Estándar de Azerbaiyán, Hora de Samara';
$lang['UP45']	= '(UTC +4:30) Afganistán';
$lang['UP5']	= '(UTC +5:00) Hora Estándar de Pakistan, Hora de Ekaterimburgo';
$lang['UP55']	= '(UTC +5:30) Hora Estándar de la India, Hora Estándar de Sri Lanka';
$lang['UP575']	= '(UTC +5:45) Hora de Nepal';
$lang['UP6']	= '(UTC +6:00) Hora Estándar de Bangladés, Hora de Bután, Hora de Omsk';
$lang['UP65']	= '(UTC +6:30) Islas Cocos, Birmania';
$lang['UP7']	= '(UTC +7:00) Hora de Krasnoyarsk, Camboya, Laos, Tailandia, Vietnam';
$lang['UP8']	= '(UTC +8:00) Hora Estándar Occidental Australiana, Hora de Pekín, Hora de Irkutsk';
$lang['UP875']	= '(UTC +8:45) Hora Estándar Centro Occidental Australiana';
$lang['UP9']	= '(UTC +9:00) Hora Estándar de Japón, Hora Estándar de Corea, Hora de Yakutsk';
$lang['UP95']	= '(UTC +9:30) Hora Estándar Central Australiana';
$lang['UP10']	= '(UTC +10:00) Hora Estándar Oriental Australiana, Hora de Vladivostok';
$lang['UP105']	= '(UTC +10:30) Isla de Lord Howe';
$lang['UP11']	= '(UTC +11:00) Hora de Srednekolimsk, Islas Salomón, Vanuatu';
$lang['UP115']	= '(UTC +11:30) Isla Norfolk';
$lang['UP12']	= '(UTC +12:00) Fiji, Islas Gilbert, Horario de Kamchatka, Hora estandar de Nueva Zelanda';
$lang['UP1275']	= '(UTC +12:45) Hora Estándar de las Islas Chatham';
$lang['UP13']	= '(UTC +13:00) Zona horaria de Samoa, Hora de las Islas Fénix, Tonga';
$lang['UP14']	= '(UTC +14:00) Islas de la Línea';
