<?php

$lang = array (
'is_exist' => '<b>%s</b> غير موجود',
'required' => '<b>%s</b> مطلوب',
'isset' => '<b>%s</b> يجب أن يكون لها قيمة',
'valid_email' => '<b>%s</b> يجب أن تحتوي على عنوان بريد إلكتروني صالح',
'valid_emails' => '<b>%s</b> يجب أن تحتوي على جميع عناوين البريد الإلكتروني صالحة',
'valid_url' => '<b>%s</b> يجب أن تحتوي على عنوان URL صالح',
'valid_ip' => '<b>%s</b> يجب أن تحتوي على IP صالح',
'min_length' => '<b>%s</b> يجب أن تكون على الأقل <b>%s</b> حرفا في الطول',
'max_length' => '<b>%s</b> لا يمكن أن يتجاوز <b>%s</b> حرفا في الطول',
'exact_length' => '<b>%s</b> يجب أن تكون بالضبط <b>%s</b> حرفا في الطول',
'alpha' => '<b>%s</b> قد تحتوي فقط على أحرف أبجدية',
'alpha_numeric' => '<b>%s</b> قد تحتوي فقط على أحرف أبجدية رقمية',
'alpha_dash' => '<b>%s</b> قد تحتوي فقط على أحرف أبجدية رقمية, يؤكد, و شرطات',
'numeric' => '<b>%s</b> يجب أن تحتوي على أرقام فقط',
'is_numeric' => '<b>%s</b> يجب أن تحتوي على أحرف رقمية فقط',
'integer' => '<b>%s</b> يجب أن تحتوي على عدد صحيح',
'regex_match' => '<b>%s</b> ليس في الشكل الصحيح',
'matches' => '<b>%s</b> لا يطابق <b>%s</b> المجال',
'is_unique' => '<b>%s</b> يستخدم بالفعل',
'is_natural' => '<b>%s</b> يجب أن تحتوي فقط على أرقام إيجابية',
'is_natural_no_zero' => '<b>%s</b> يجب أن تحتوي على عدد أكبر من الصفر',
'decimal' => '<b>%s</b> يجب أن تحتوي على عدد عشري',
'less_than' => '<b>%s</b> يجب أن تحتوي على عدد أقل من <b>%s</b>',
'greater_than' => '<b>%s</b> يجب أن تحتوي على عدد أكبر من <b>%s</b>',
);





?>
