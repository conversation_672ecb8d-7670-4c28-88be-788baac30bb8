<?php 

$lang = array (
  'Cron Job' => 'Cron Job',
  'do you want to get email alert for unread messages ?' => '¿Quieres recibir alertas por correo electrónico para los mensajes no leídos?',
  'this campaign is already enable for processing.' => 'esta campaña ya está habilitada para el procesamiento.',
  'you have not enter any domain name or FB page url' => 'no ha ingresado ningún nombre de dominio o URL de página FB',
  'login with facebook' => 'iniciar sesión con facebook',
  'login with google' => 'iniciar sesión con google',
  'user access token is valid. you can login and get new user access token if you want.' => 'el token de acceso del usuario es válido. puede iniciar sesión y obtener un nuevo token de acceso de usuario si lo desea.',
  'go back' => 'volver',
  'tag user' => 'usuario de etiqueta',
  'You can tag user in your comment reply. Facebook will notify them about mention whenever you tag.' => 'Puede etiquetar al usuario en su respuesta de comentario. Facebook les notificará acerca de las menciones cada vez que etiquete.',
  'delay used in auto-reply (seconds)' => 'retraso utilizado en respuesta automática (segundos)',
  'auto-reply campaign live duration (days)' => 'duración en vivo de la campaña de respuesta automática (días)',
  'write your message which you want to send. You can customize the message by individual commenter name.' => 'escribe tu mensaje que deseas enviar Puede personalizar el mensaje por nombre de comentarista individual.',
  'Campaign have been submitted successfully.' => 'La campaña se ha enviado correctamente.',
  'See report' => 'Ver informe',
);