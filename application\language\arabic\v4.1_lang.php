<?php

$lang = array (
'Cron Job' => 'وظيفة كرون',
'do you want to get email alert for unread messages ?' => 'هل تريد الحصول على تنبيه عن طريق البريد الالكتروني للبريد غير المقروء ؟',
'this campaign is already enable for processing.' => 'هذه الحملة مفعلة بالفعل للمعالجة.',
'you have not enter any domain name or FB page url' => 'دون إدخال أي اسم نطاق أو الفيس بوك رابط الصفحة',
'login with facebook' => 'تسجيل الدخول بواسطة فيسبوك',
'login with google' => 'تسجيل الدخول مع جوجل',
'user access token is valid. you can login and get new user access token if you want.' => 'وصول المستخدم رمزي صالح. يمكنك تسجيل الدخول والحصول على مستخدم جديد رمز وصول إذا كنت تريد.',
'go back' => 'العودة',
'tag user' => 'الوسم المستخدم',
'You can tag user in your comment reply. Facebook will notify them about mention whenever you tag.' => 'يمكنك الوسم المستخدم في تعليقك الرد. Facebook يخطر لهم عن ذكر كلما الوسم.',
'delay used in auto-reply (seconds)' => 'تأخير المستخدمة في الرد التلقائي (بالثواني)',
'auto-reply campaign live duration (days)' => 'الرد التلقائي حملة يعيش المدة (بالأيام)',
'write your message which you want to send. You can customize the message by individual commenter name.' => 'اكتب الرسالة التي تريد إرسالها. يمكنك تخصيص الرسالة باضافة اسم الشخص صاحب التعليق.',
'Campaign have been submitted successfully.' => 'الحملة قدمت بنجاح.',
'See report' => 'انظر تقرير',
);





?>
