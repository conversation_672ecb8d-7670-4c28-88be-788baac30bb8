<?php 

$lang = array (
  'Cron Job' => 'Cron Job',
  'do you want to get email alert for unread messages ?' => 'Souhaitez-vous recevoir une alerte par e-mail pour les messages non lus?',
  'this campaign is already enable for processing.' => 'cette campagne est déjà activée pour le traitement.',
  'you have not enter any domain name or FB page url' => 'vous n\'avez entré aucun nom de domaine ou URL de page FB',
  'login with facebook' => 'connectez-vous avec facebook',
  'login with google' => 'connectez-vous avec google',
  'user access token is valid. you can login and get new user access token if you want.' => 'Le jeton d\'accès utilisateur est valide. vous pouvez vous connecter et obtenir un nouveau jeton d\'accès si vous le souhaitez.',
  'go back' => 'revenir en arrière',
  'tag user' => 'tag utilisateur',
  'You can tag user in your comment reply. Facebook will notify them about mention whenever you tag.' => 'Vous pouvez marquer l\'utilisateur dans votre réponse. Facebook les informera de la mention à chaque fois que vous marquez.',
  'delay used in auto-reply (seconds)' => 'délai utilisé dans la réponse automatique (secondes)',
  'auto-reply campaign live duration (days)' => 'durée de la campagne de réponse automatique (en jours)',
  'write your message which you want to send. You can customize the message by individual commenter name.' => 'écrivez votre message que vous voulez envoyer. Vous pouvez personnaliser le message par nom de l\'auteur.',
  'Campaign have been submitted successfully.' => 'La campagne a été soumise avec succès.',
  'See report' => 'Voir le rapport',
);