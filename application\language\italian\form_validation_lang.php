<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * <AUTHOR>
 * @copyright	Copyright (c) 2014 - 2016, British Columbia Institute of Technology (http://bcit.ca/)
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	http://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');
$lang['is_exist']			= "এই <b>%s</b> non esiste"; // User-defind (Created by Alamin)
$lang['required'] = 'Il campo <b>%s</b> è necessario.';
$lang['isset'] = 'Il campo <b>%s</b> deve avere un valore.';
$lang['valid_email'] = 'Il campo <b>%s</b> deve contenere un indirizzo email valido.';
$lang['valid_emails'] = 'Il campo <b>%s</b> deve contenere tutti indirizzi email validi.';
$lang['valid_url'] = 'Il campo <b>%s</b> deve contenere un URL valido.';
$lang['valid_ip'] = 'Il campo <b>%s</b> deve contenenre un indirizzo IP valido.';
$lang['min_length'] = 'Il campo <b>%s</b> deve essere composto da almeno <b>%s</b> caratteri.';
$lang['max_length'] = 'Il campo <b>%s</b> deve essere composto da meno di <b>%s</b> caratteri.';
$lang['exact_length'] = 'Il campo <b>%s</b> deve essere composto esattamente da <b>%s</b> caratetteri.';
$lang['alpha'] = 'Il campo <b>%s</b> deve contenere solo caratterti alfabetici.';
$lang['alpha_numeric'] = 'Il campo <b>%s</b> deve contenere solo caratteri alfa-numerici.';
$lang['alpha_numeric_spaces'] = 'Il campo <b>%s</b> deve contenere solo caratteri alfa-numerici e spazi.';
$lang['alpha_dash'] = 'Il campo <b>%s</b> deve contenere solo caratteri alfa-numerici, underscore e trattini.';
$lang['numeric'] = 'Il campo <b>%s</b> deve contenere un numero.';
$lang['is_numeric'] = 'Il campo <b>%s</b> deve contenere un numero.';
$lang['integer'] = 'Il campo <b>%s</b> deve contenere un intero.';
$lang['regex_match'] = 'Il campo <b>%s</b> non è stato inserito nella forma corretta.';
$lang['matches'] = 'Il campo <b>%s</b> non è uguale al campo <b>%s</b>.';
$lang['differs'] = 'Il campo <b>%s</b> deve essere differente dal campo <b>%s</b>.';
$lang['is_unique'] = 'Il campo <b>%s</b> deve contenere un valore univoco.';
$lang['is_natural'] = 'Il campo <b>%s</b> deve contenenre un numero.';
$lang['is_natural_no_zero'] = 'Il campo <b>%s</b> deve contenere un numero più grande di zero.';
$lang['decimal'] = 'Il campo <b>%s</b> deve contenere un numero decimale.';
$lang['less_than'] = 'Il campo <b>%s</b> deve contenere un numero inferiore a <b>%s</b>.';
$lang['less_than_equal_to'] = 'Il campo <b>%s</b> deve contenere un numero inferiore o uguale a <b>%s</b>.';
$lang['greater_than'] = 'Il campo <b>%s</b> deve contenere un numero maggiore di <b>%s</b>.';
$lang['greater_than_equal_to'] = 'Il campo <b>%s</b> deve contenere un numero maggiore o uguale a <b>%s</b>.';
$lang['error_message_not_set']  = 'Non è stato possibile trovare il messaggio di errore relativo al campo <b>%s</b>';
$lang['in_list']		= 'Il campo <b>%s</b> può contenere solo uno dei seguenti valori: <b>%s</b>.';
