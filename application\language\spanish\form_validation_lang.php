<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * <AUTHOR>
 * @copyright	Copyright (c) 2014 - 2016, British Columbia Institute of Technology (http://bcit.ca/)
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	http://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');
$lang['is_exist']			= "এই <b>%s</b> no existe"; // User-defind (Created by Alamin)
$lang['required']		= 'El campo <b>%s</b> es obligatorio.';
$lang['isset']			= 'El campo <b>%s</b> debe contener un valor.';
$lang['valid_email']		= 'El campo <b>%s</b> debe contener un email válido.';
$lang['valid_emails']		= 'El campo <b>%s</b> debe contener todos los emails válidos.';
$lang['valid_url']		= 'El campo <b>%s</b> debe contener una URL válida.';
$lang['valid_ip']		= 'El campo <b>%s</b> debe contener una IP válida.';
$lang['min_length']		= 'El campo <b>%s</b> debe ser de al menos <b>%s</b> caracteres de longitud.';
$lang['max_length']		= 'El campo <b>%s</b> no puede superar los <b>%s</b> caracteres de longitud.';
$lang['exact_length']		= 'El campo <b>%s</b> debe ser de exactamente <b>%s</b> caracteres de longitud.';
$lang['alpha']			= 'El campo <b>%s</b> solo puede contener caracteres alfabéticos.';
$lang['alpha_numeric']		= 'El campo <b>%s</b> solo puede contener caracteres alfanuméricos.';
$lang['alpha_numeric_spaces']	= 'El campo <b>%s</b> solo puede contener caracteres alfanuméricos y espacios.';
$lang['alpha_dash']		= 'El campo <b>%s</b> solo puede contener caracteres alfanuméricos, guiones bajos y guiones.';
$lang['numeric']		= 'El campo <b>%s</b> solo puede contener números.';
$lang['is_numeric']		= 'El campo <b>%s</b> solo puede contener caracteres numéricos.';
$lang['integer']		= 'El campo <b>%s</b> debe contener un entero.';
$lang['regex_match']		= 'El campo <b>%s</b> no está en el formato correcto.';
$lang['matches']		= 'El campo <b>%s</b> no coincide con el campo <b>%s</b>.';
$lang['differs']		= 'El campo <b>%s</b> debe ser distinto al campo <b>%s</b>.';
$lang['is_unique'] 		= 'El campo <b>%s</b> debe contener un valor único.';
$lang['is_natural']		= 'El campo <b>%s</b> solo puede contener dígitos.';
$lang['is_natural_no_zero']	= 'El campo <b>%s</b> solo puede contener dígitos y debe ser mayor que cero.';
$lang['decimal']		= 'El campo <b>%s</b> debe contener un número decimal.';
$lang['less_than']		= 'El campo <b>%s</b> debe contener un número menor que <b>%s</b>.';
$lang['less_than_equal_to']	= 'El campo <b>%s</b> debe contener un número menor o igual a <b>%s</b>.';
$lang['greater_than']		= 'El campo <b>%s</b> debe contener un número mayor que <b>%s</b>.';
$lang['greater_than_equal_to']	= 'El campo <b>%s</b> debe contener un número mayor o igual a <b>%s</b>.';
$lang['error_message_not_set']	= 'No se ha podido acceder al mensaje de error correspondiente para el campo <b>%s</b>.';
$lang['in_list']		= 'El campo <b>%s</b> debe contener uno de estos: <b>%s</b>.';
