<?php
/**
 * System messages translation for CodeIgniter(tm)
 * <AUTHOR> community
 * @copyright	Copyright (c) 2014 - 2016, British Columbia Institute of Technology (http://bcit.ca/)
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	http://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['upload_userfile_not_set']         = "Impossible de trouver une variable de type POST nommée userfile.";
$lang['upload_file_exceeds_limit']       = "Le fichier envoyé dépasse la taille limite définie dans votre fichier de configuration PHP.";
$lang['upload_file_exceeds_form_limit']  = "Le fichier chargé dépasse la taille limite définie par le formulaire d'envoi.";
$lang['upload_file_partial']             = "Le fichier n'a été que partiellement envoyé.";
$lang['upload_no_temp_directory']        = "Le dossier temporaire manque.";
$lang['upload_unable_to_write_file']     = "Impossible d'écrire le fichier sur disque.";
$lang['upload_stopped_by_extension']     = "Le chargement du fichier a été arrêté par extension.";
$lang['upload_no_file_selected']         = "Vous n'avez pas sélectionné de fichier à envoyer.";
$lang['upload_invalid_filetype']         = "Le type de fichier que vous tentez d'envoyer n'est pas autorisé.";
$lang['upload_invalid_filesize']         = "Le fichier que vous tentez d'envoyer est plus gros que la taille autorisée.";
$lang['upload_invalid_dimensions']       = "L'image que vous tentez d'envoyer dépasse les valeurs maximales autorisées pour la hauteur ou la largeur.";
$lang['upload_destination_error']        = "Une erreur est survenue lors du déplacement du fichier envoyé vers sa destination finale.";
$lang['upload_no_filepath']              = "Le chemin de destination semble invalide.";
$lang['upload_no_file_types']            = "Vous n'avez pas spécifié les types de fichier autorisés.";
$lang['upload_bad_filename']             = "Un fichier avec le même nom que celui que vous avez envoyé existe déjà sur le serveur.";
$lang['upload_not_writable']             = "Le répertoire de destination ne semble pas être accessible en écriture.";
