<?php 

$lang = array (
  'Cron Job' => 'Cron Job',
  'do you want to get email alert for unread messages ?' => 'Do you want to get email alert for unread messages?',
  'this campaign is already enable for processing.' => 'This campaign is already enable for processing.',
  'you have not enter any domain name or FB page url' => 'You have not entered any domain name or FB page url',
  'login with facebook' => 'Login with Facebook',
  'login with google' => 'Login with Google',
  'user access token is valid. you can login and get new user access token if you want.' => 'User access token is valid. you can login and get new user access token if you want.',
  'go back' => 'Go back',
  'tag user' => 'Tag user',
  'You can tag user in your comment reply. Facebook will notify them about mention whenever you tag.' => 'You can tag user in your comment reply. Facebook will notify them about mention whenever you tag.',
  'delay used in auto-reply (seconds)' => 'Delay used in auto-reply (seconds)',
  'auto-reply campaign live duration (days)' => 'Auto-reply campaign live duration (days)',
  'write your message which you want to send. You can customize the message by individual commenter name.' => 'Write your message which you want to send. You can customize the message by individual commenter name.',
  'Campaign have been submitted successfully.' => 'Campaign have been submitted successfully.',
  'See report' => 'See report',
);