<?php 
$lang = array (
  'auto lead list sync cron job command [once per day]' => 'Auto Lead List Sync Cron Job Command [once per day]',
  'membership expiry alert cron job command [once per day]' => 'Membership Expiry Alert Cron Job Command [once per day]',
  'send inbox messages cron job command [once per minute or higher]' => 'Send Inbox Message Cron Job Command [once per 1 minute or higher]',
  'alert for unread messages cron job command [once per hour or higher]' => 'Unread Message Alert Cron job command [once per hour or higher]',
  'CTA poster cron job command [once per hour or higher]' => 'CTA poster Cron job command [once per hour or higher]',
  'please select your time zone' => 'Please select your time zone',
  'do you have FB business manager account ?' => 'Do you have FB business manager account ?',
  'do you have FB business manager account' => 'Do you have FB business manager account',
  'time interval for getting email alert' => 'Time interval for getting email alert',
  'email address to which alert will be sent' => 'Email address to which alert will be sent',
  'you have no page in facebook' => 'You have no page in facebook',
  'auto private reply : page list' => 'Auto Private Reply : Page List',
  'campaign name' => 'Campaign Name',
  'reply sent' => 'Reply Sent',
  'post create time' => 'Post Create Time',
  'last reply time' => 'Last Reply Time',
  'error message' => 'Error Message',
  'post description' => 'Post Description',
  'total auto reply enabled post' => 'Total Auto Reply Enabled Post',
  'total auto reply sent' => 'Total Auto Reply Sent',
  'edit reply by post id' => 'Edit Reply by Post ID',
  'get latest posts & enable auto reply' => 'Get latest posts & enable auto reply',
  'view report' => 'View report',
  'enable reply by post id' => 'Enable Reply by Post ID',
  'last auto reply sent' => 'Last auto reply sent',
  'send message - button generator' => 'Send Message - Button generator',
  'choose among default images or put image url' => 'Choose among default images or put image url',
  'choose pages' => 'Choose Pages',
  'select page' => 'Select Page',
  'get embed code and see preview' => 'Get embed code and see preview',
  'copy embed code and paste it in your site' => 'Copy embed code and paste it in your site',
  'Copy embed code' => 'Copy embed code',
  'messanger AD JSON script' => 'Messenger AD JSON Script',
  'image url' => 'Image Url',
  'upload image' => 'Upload Image',
  'upload' => 'Upload',
  'title' => 'Title',
  'sub-title' => 'Sub-Title',
  'website url' => 'Website Url',
  'view website button text' => 'View website button text',
  'start chatting button text' => 'Start chatting button text',
  'quick reply button 1 text' => 'Quick reply button 1 text',
  'quick reply button 2 text' => 'Quick reply button 2 text',
  'get JSON code' => 'Get JSON code',
  'inbox preview' => 'Inbox Preview',
  'page name' => 'Page Name',
  'message title' => 'Message Title',
  'message subtitle' => 'Message subtitle',
  'subtitle' => 'subtitle',
  'view website' => 'View Website',
  'start chatting' => 'Start Chatting',
  'ok, thanks' => 'Ok, thanks',
  'no, thanks' => 'No, thanks',
  'reply1' => 'Reply1',
  'reply2' => 'Reply2',
  'please copy the following JSON code for farther use' => 'Please copy the following JSON code for farther use',
  'sorry, your bulk limit is exceeded for this module.' => 'sorry, your bulk limit is exceeded for this module.',
  'click here to see usage log' => 'click here to see usage log',
  'please provide all the information' => 'Please provide all the information',
  'CTA post campaign cist' => 'CTA Post Campaign List',
  'CTA button' => 'CTA Button',
  'scheduled at' => 'Scheduled at',
  'status' => 'Status',
  'visit post' => 'Visit Post',
  'delete' => 'Delete',
  'message' => 'Message',
  'new CTA post campaign' => 'New CTA Post Campaign',
  'search report' => 'Search Report',
  'domain name' => 'Domain Name',
  'message header' => 'Message Header',
  'javascript code' => 'JavaScript Code',
  'fb page url' => 'FB Page Url',
  'scheduled time' => 'Scheduled Time',
  'created at' => 'Created at',
  'page name(s)' => 'Page Name(s)',
  'client id' => 'Client ID',
  'client username' => 'Client Username',
  'message thread id' => 'Message Thread ID',
  'multi-group campaign' => 'Multi-group Campaign',
  'multi-group' => 'Multi-group',
  'include' => 'Include',
  'paste url' => 'Paste URL',
  'will be attached & previewed' => 'will be attached & previewed',
  'total lead selected' => 'Total Lead Selected',
  'exclude these leads' => 'Exclude these leads',
  'delay time (seconds)' => 'Delay time (seconds)',
  '0 means random' => '0 means random',
  'embed unsubscribe link' => 'Embed unsubscribe link',
  'disable' => 'Disable',
  'enable' => 'Enable',
  'schedule' => 'Schedule',
  'now' => 'Now',
  'later' => 'Later',
  'time zone' => 'Time zone',
  'submit campaign' => 'Submit Campaign',
  'your message goes here...' => 'Your message goes here...',
  'send test message' => 'Send Test Message',
  'choose up to 3 leads to test how it will look. start typing, it\'s auto-complete.' => 'Choose up to 3 leads to test how it will look. Start typing, it\'s auto-complete.',
  'multi-page campaign' => 'Multi-page Campaign',
  'lead list' => 'Lead List',
  'bulk group assign' => 'Bulk Group Assign',
  'bulk delete contact' => 'Bulk Delete Contact',
  'all groups' => 'All Groups',
  'lead group' => 'Lead Group',
  'select lead group' => 'Select Lead Group',
  'subscribed?' => 'Subscribed?',
  'action' => 'Action',
  'lifetime summary' => 'Lifetime Summary',
  'total subscriber' => 'Total Subscriber',
  'total unsubscriber' => 'Total Unsubscriber',
  'total messages sent' => 'Total Messages Sent',
  'campaign completed' => 'Campaign Completed',
  'completed' => 'Completed',
  'campaign processing' => 'Campaign Processing',
  'processing' => 'Processing',
  'campaign pending' => 'Campaign Pending',
  'pending' => 'Pending',
  'instantly posted' => 'Instantly posted',
  'visit' => 'Visit',
  'this post is not published yet.' => 'This post is not published yet.',
  'message sent vs campaign created report for last 12 months' => 'MESSAGE SENT VS CAMPAIGN CREATED REPORT FOR LAST 12 MONTHS',
  'pending campaign' => 'Pending Campaign',
  'recently completed campaign' => 'Recently Completed Campaign',
  'monthly summary' => 'Monthly Summary',
  'campaign complete' => 'Campaign Complete',
  'lead generation information' => 'Lead Generation Information',
  'last auto reply' => 'Last Auto Reply',
  'auto reply enabled post' => 'Auto Reply Enabled Post',
  'auto reply sent' => 'Auto Reply Sent',
  'chat plugin enabled' => 'Chat Plugin Enabled',
  'sl' => 'SN',
  'reply to' => 'Reply to',
  'reply time' => 'Reply time',
  'post name' => 'Post Name',
  'schedule time' => 'Schedule Time',
  'selected message' => 'Selected Message',
  'add facebook account' => 'Add Facebook Account',
  'how to get FB numeric ID?' => 'How to get FB numeric ID?',
  'send app request' => 'Send App Request',
  'your existing accounts' => 'Your existing accounts',
  'refresh your access token' => 'Refresh your access token',
  'total pages' => 'Total Pages',
  'total groups' => 'Total Groups',
  'remove this account' => 'Remove this account',
  'page list' => 'Page List',
  'analytics' => 'Analytics',
  'delete confirmatation' => 'Delete Confirmatation',
  'do you want to delete this group from database?' => 'Do you want to delete this group from database?',
  'if you delete this page, all the campaigns corresponding to this page will also be deleted. do you want to delete this page from database?' => 'If you delete this page, all the campaigns corresponding to this page will also be deleted. Do you want to delete this page from database?',
  'if you delete this account, all the pages, groups and all the campaigns corresponding to this account will also be deleted form database. do you want to delete this account from database?' => 'If you delete this account, all the pages, groups and all the campaigns corresponding to this account will also be deleted form database. Do you want to delete this account from database?',
  'please enter your facebook numeric id first' => 'Please Enter Your Facebook Numeric ID First',
  'do you want to remove this account from our database? you can import again.' => 'Do you want to remove this account from our database? You can import again.',
  'do you want to remove this page from our database?' => 'Do you want to remove this page from our database?',
  'you must be logged in your facebook account for which you want to refresh your access token. for synch your new page, simply refresh your token. if any access token is restricted for any action, refresh your access token.' => 'You must be logged in your facebook account for which you want to refresh your access token. For synch your new page, simply refresh your token. If any access token is restricted for any action, refresh your access token.',
  'import lead(s) : page list' => 'Import Lead(s) : Page List',
  'subscribed' => 'Subscribed',
  'unsubscribed' => 'Unsubscribed',
  'scan page inbox' => 'Scan page inbox',
  'disable daily auto scan' => 'Disable daily auto scan',
  'enable daily auto scan' => 'Enable daily auto scan',
  'daily auto scan has been disabled successfully.' => 'Background scanning has been disabled successfully.',
  'daily auto scan has been enabled successfully.' => 'Background scanning has been enabled successfully.',
  'subscribed & unsubscibed' => 'Subscribed & Unsubscibed',
  'only unsubscribed' => 'Only Unsubscribed',
  'all pages' => 'All Pages',
  'do you want to delete this contact?' => 'Do you want to delete this contact?',
  'you have not selected any lead to assign group. you can choose upto' => 'You have not selected any lead to assign group. You can choose upto',
  'leads at a time.' => 'leads at a time.',
  'you can select upto' => 'You can select upto',
  'leads' => 'leads',
  'your group has been deleted successfully.' => 'Your group has been deleted successfully.',
  'something went wrong, please try again.' => 'Something went wrong, please try again.',
  'your page and all of it\'s corresponding campaigns has been deleted successfully.' => 'Your page and all of it\'s corresponding campaigns has been deleted successfully.',
  'success' => 'success',
  'please log in & check your facebook profile page notifications, to accept our invitation' => 'Please log in & check your facebook profile page notifications, to accept our invitation',
  'a request has been sent to your facebook account. please login to your facebook account, confirm the app request and click below button.' => 'A request has been sent to your facebook account. Please login to your facebook account, confirm the app request and click below button.',
  'do not click this until confirmed' => 'DO NOT CLICK THIS UNTIL CONFIRMED',
  'i\'ve confirmed app request in facebook' => 'I\'ve Confirmed App Request in Facebook',
  'something went wrong, please try with correct information.' => 'Something went wrong, please try with correct information.',
  'something went wrong,please' => 'Something went wrong,please',
  'try again' => 'try again',
  '' => 'Sorry, your account import limit has been exceeded.',
  'sorry, you didn\'t confirm the request yet. please login to your fb account and accept the request. for more' => 'Sorry, you didn\'t confirm the request yet. Please login to your FB account and accept the request. For mor',
  'visit here.' => 'Visit Here.',
  'enter your facebook numeric id' => 'Enter Your Facebook Numeric ID',
  'the number of people talking about the page by user country (unique users) [last 28 days]' => 'The Number of People Talking About the Page by User Country (Unique Users) [Last 28 days]',
  'total page reach by user country. (unique users) [last 28 days]' => 'Total Page Reach by user country. (Unique Users) [Last 28 days]',
  'the number of people talking about the page by user city. (unique users) [last 28 days]' => 'The number of People Talking About the Page by user city. (Unique Users) [Last 28 Days]',
  'the number of people who liked your page and who were online on the specified day. (unique users)' => 'The number of people who liked your Page and who were online on the specified day. (Unique Users)',
  'this is a breakdown of the number of page likes from the most common places where people can like your page. (total count) [last 28 days]' => 'This is a breakdown of the number of Page likes from the most common places where people can like your Page. (Total Count) [Last 28 days]',
  'daily: the number of impressions that came from all of your posts. (total count)' => 'Daily: The number of impressions that came from all of your posts. (Total Count)',
  'organic impression : the number of impressions of your posts in news feed or ticker or on your page. (total count)' => 'Organic Impression : The number of impressions of your posts in News Feed or ticker or on your Page. (Total Count)',
  'paid impression : the number of impressions of your page posts in an ad or sponsored story. (total count)' => 'Paid Impression : The number of impressions of your Page posts in an ad or sponsored story. (Total Count)',
  'tab' => 'Tab',
  'views' => 'views',
  'tabs on your page that were viewed when logged-in users visited your page. (unique users) [last 28 days]' => 'Tabs on your Page that were viewed when logged-in users visited your Page. (Unique Users) [Last 28 Days]',
  'referrar' => 'Referrer',
  'city' => 'City',
  'total' => 'Total',
  'total page reach by user city. (unique users) [last 28 days]' => 'Total Page Reach by user city. (Unique Users) [Last 28 days]',
  'top referring external domains sending traffic to your page (total count) [last 28 days]' => 'Top referring external domains sending traffic to your Page (Total Count) [Last 28 Days]',
  'page storytellers by story type' => 'Page storytellers by story type',
  'page impression paid vs non-paid' => 'Page impression paid vs non-paid',
  'page impression organic' => 'Page impression organic',
  'page impression by user country' => 'Page impression by user country',
  'page storytellers by user country' => 'Page storytellers by user country',
  'page impression by user city' => 'Page impression by user city',
  'page engaged users' => 'page engaged users',
  'page consumptions by type' => 'page consumptions by type',
  'page positive feedback by type' => 'page positive feedback by type',
  'page negative feedback by type' => 'page negative feedback by type',
  'page fans online per day' => 'page fans online per day',
  'page fans by like source' => 'page fans by like source',
  'page likes vs unlikes' => 'page likes vs unlikes',
  'page posts impressions' => 'page posts impressions',
  'page posts impressions paid vs organic' => 'page posts impressions paid vs organic',
  'page tab views' => 'page tab views',
  'page views by external referral' => 'page views by external referral',
  'description' => 'Description',
  'what is this?' => 'What is this?',
  'post insight : page list' => 'Post Insight : Page List',
  'post list' => 'Post List',
  'sync post for page' => 'Sync post for page',
  'video insight' => 'Video Insight',
  'thumbnail' => 'Thumbnail',
  'url' => 'Url',
  'upload time' => 'Upload Time',
  'video id' => 'Video ID',
  'video description' => 'Video Description',
  'search' => 'search',
  'video insight : page list' => 'Video Insight : Page List',
  'sync video' => 'Sync Video',
  'video list' => 'Video List',
  'do you want to sync videos for this page?' => 'Do you want to sync videos for this page?',
  'sync videos for page' => 'Sync videos for page',
  'page storyteller by user city' => 'page storyteller by user city',
  'hi' => 'hi',
  'never scanned' => 'Never scanned',
  'leads has been imported successfully.' => 'leads has been imported successfully.',
  'last scanned' => 'Last Scanned',
  'user name' => 'User Name',
  'facebook link' => 'Facebook Link',
  'added at' => 'Added at',
  'group' => 'Group',
  'unsubscribe' => 'Unsubscribe',
  'subscribe' => 'Subscribe',
  'you have not selected any lead to assign group.' => 'You have not selected any lead to assign group.',
  'you have not selected any lead group.' => 'You have not selected any lead group.',
  'groups have been assigned successfully.' => 'Groups have been assigned successfully.',
  'you have not selected any lead to delete. you can choose upto' => 'You have not selected any lead to delete. You can choose upto',
  'leads at atime' => 'leads at atime',
  'contacts have been deleted successfully.' => 'Contacts have been deleted successfully.',
  'you have not select any contact.' => 'You have not select any contact.',
  'export' => 'Export',
  'download' => 'Download',
  'export contact (CSV)' => 'Export Contact (CSV)',
  'your file is ready to download' => 'Your file is ready to download',
  'close' => 'Close',
  'cancel' => 'Cancel',
  'assign group' => 'Assign Group',
  'bulk delete contact confirmation' => 'Bulk Delete Contact Confirmation',
  'do you want to delete' => 'Do you want to delete',
  'contacts from database?' => 'contacts from database?',
  'delete contacts' => 'Delete Contacts',
  'please wait' => 'Please Wait',
  'leads. You have selected' => 'leads. You have selected',
  'leads.' => 'leads.',
  'please provide a post id of page' => 'Please Provide a Post ID of Page',
  'post id' => 'Post ID',
  'edit auto reply' => 'Edit Auto Reply',
  'enable auto reply' => 'Enable Auto Reply',
  'last name' => 'Last Name',
  'private reply if no matching found' => 'Private reply if no matching found',
  'comment reply if no matching found' => 'Comment reply if no matching found',
  'add more filtering' => 'Add more filtering',
  'msg for comment reply' => 'Message for comment reply',
  'msg for private reply' => 'Msg for private reply',
  'private reply' => 'Private Reply',
  'filter word/sentence' => 'Filter Word/Sentence',
  'please give the following information for post auto private reply' => 'Please give the following information for post auto private reply',
  'do you want to send reply message to a user multiple times?' => 'Do you want to send reply message to a user multiple times?',
  'do you want to enable comment reply?' => 'Do you want to enable comment reply?',
  'do you want to like on comment by page?' => 'Do you want to like on comment by page?',
  'auto reply campaign name' => 'Auto reply campaign name',
  'message for private reply' => 'Message for private reply',
  'latest post for page' => 'Latest posts for page',
  'scheduled from' => 'Scheduled From',
  'scheduled to' => 'Scheduled To',
  'search campaign' => 'Search Campaign',
  'campaign report' => 'Campaign Report',
  'something went wrong.' => 'Something went wrong.',
  'do you really want to delete this campaign?' => 'Do you really want to delete this campaign?',
  'email notification settings' => 'Email Notification Settings',
  'yes' => 'Yes',
  'no' => 'No',
  'generic message for all' => 'Generic message for all',
  'send message by filtering word/sentence' => 'Send message by filtering word/sentence',
  'write your content here' => 'Write your content here',
  'write your auto reply campaign name here' => 'Write your auto reply campaign name here',
  'write your message which you want to send based on filter words. You can customize the message by individual commenter name.' => 'write your message which you want to send based on filter words. You can customize the message by individual commenter name.',
  'You can include #LEAD_USER_LAST_NAME# variable inside your message. The variable will be replaced by real names when we will send it.' => 'You can include #LEAD_USER_LAST_NAME# variable inside your message. The variable will be replaced by real names when we will send it.',
  'You can include #LEAD_USER_FIRST_NAME# variable inside your message. The variable will be replaced by real names when we will send it.' => 'You can include #LEAD_USER_FIRST_NAME# variable inside your message. The variable will be replaced by real names when we will send it.',
  'Write the word or sentence for which you want to filter comment. For multiple filter keyword write comma separated. Example -   why, wanto to know, when' => 'Write the word or sentence for which you want to filter comment. For multiple filter keyword write comma separated. Example -   why, wanto to know, when',
  'write your filter word here' => 'write your filter word here',
  'Write the message,  if no filter word found. If you don\'t want to send message them, just keep it blank .' => 'Write the message,  if no filter word found. If you don\'t want to send message them, just keep it blank .',
  'Write the word or sentence for which you want to filter comment. For multiple filter keyword write comma separated. Example -   why, want to know, when' => 'Write the word or sentence for which you want to filter comment. For multiple filter keyword write comma separated. Example -   why, want to know, when',
  'please give a post id' => 'Please give a post ID',
  'check existance' => 'Check Existance',
  'you didn\'t provide all information.' => 'You didn\'t provide all information.',
  'you didn\\\'t select any option.' => 'You didn\\\'t select any option.',
  'already enabled' => 'Already Enabled',
  'This post ID is not found in database or this post ID is not associated with the page you are working.' => 'This post ID is not found in database or this post ID is not associated with the page you are working.',
  'created time' => 'Created Time',
  'enabled' => 'Enabled',
  'please provide correct post id.' => 'Please provide correct post ID.',
  'report of auto reply' => 'Report of Auto Reply',
  'Write the word or sentence for which you want to filter comment. For multiple filter keyword write comma separated. Example -  why, want to know, when' => 'Write the word or sentence for which you want to filter comment. For multiple filter keyword write comma separated. Example -  why, want to know, when',
  'auto reply report' => 'Auto Reply Report',
  'do you want to pause this campaign?' => 'Do you want to pause this campaign?',
  'do you want to delete this record from database?' => 'Do you want to delete this record from database?',
  'you didn\'t select any option.' => 'You didn\'t select any option.',
  'name' => 'Name',
  'comment' => 'Comment',
  'comment time' => 'Comment Time',
  'private reply message' => 'Private Reply Message',
  'comment reply message' => 'Comment Reply Message',
  'private reply status' => 'Private Reply Status',
  'comment reply status' => 'Comment Reply Status',
  'no data to show' => 'No data to show',
  'include lead user first name' => 'Include lead user first name',
  'your given information has been updated successfully.' => 'Your given information has been updated successfully.',
  'You must have to choose a page for which you want the \'Send Message\' button.' => 'You must have to choose a page for which you want the \'Send Message\' button.',
  'choose image' => 'Choose Image',
  'You can choose among our default button images or can put an image url. Remember that image url has higher priority than default images.' => 'You can choose among our default button images or can put an image url. Remember that image url has higher priority than default images.',
  'Copy this code and paste it in your web pages to show \'Send Message\' button. Clicking this button will take your visitors to messenger for sending message in your page. You can see preview below to ensure how it will look.' => 'Copy this code and paste it in your web pages to show \'Send Message\' button. Clicking this button will take your visitors to messenger for sending message in your page. You can see preview below to ensure how it will look.',
  'Please a page to generate \'Send Message\' button.' => 'Please a page to generate \'Send Message\' button.',
  'Please choose among default images or put link button.' => 'Please choose among default images or put link button.',
  'welcome' => 'Welcome',
  'some of the aliases you requested do not exist' => 'Some of the aliases you requested do not exist',
  'enable page for inbox & notification' => 'Enable Page : Inbox & Notification',
  'do you want to' => 'Do you want to',
  'messenger manager for this page ?' => 'messenger manager for this page ?',
  'sent from' => 'Sent From',
  'sent time' => 'Sent Time',
  'total count' => 'Total Count',
  'total unread count' => 'Total Unread Count',
  'see conversation & reply' => 'See Conversation & Reply',
  'go to inbox' => 'Go to inbox',
  'all unread messages' => 'All Unread Messages',
  'do you really want to delete this post from our database?' => 'Do you really want to delete this post from our database?',
  'facebook CTA post has been performed successfully.' => 'Facebook CTA post has been performed successfully.',
  'facebook CTA post campaign has been created successfully.' => 'Facebook CTA post campaign has been created successfully.',
  'something went wrong. Facebook CTA post campaign has been failed.' => 'Something went wrong. Facebook CTA post campaign has been failed.',
  'cta (call to action) poster' => 'CTA (Call to Action) Poster',
  'paste link' => 'Paste link',
  'preview image url' => 'Preview image URL',
  'cta button type' => 'CTA button type',
  'cta button action link' => 'CTA button action link',
  'post to pages' => 'Post to pages',
  'auto share this post' => 'Auto share this post',
  'auto share to timeline' => 'Auto share to timeline',
  'auto share as pages' => 'Auto share as pages',
  'auto private reply on user comments' => 'Auto private reply on user comments',
  'auto comment' => 'Auto comment',
  'submit post' => 'Submit Post',
  'preview' => 'Preview',
  'message page' => 'Message Page',
  'auto post campaign status' => 'Auto Post Campaign Status',
  'please paste a link to post.' => 'Please paste a link to post.',
  'please select cta button type and enter cta button action link.' => 'Please select cta button type and enter cta button action link.',
  'please select pages to publish this post.' => 'Please select pages to publish this post.',
  'please select timeline or page(s) for auto sharing.' => 'Please select timeline or page(s) for auto sharing.',
  'please type private reply message.' => 'Please type private reply message.',
  'please type auto comment message.' => 'Please type auto comment message.',
  'please select schedule time/time zone.' => 'Please select schedule time/time zone.',
  'your data has been successfully stored into the database.' => 'Your data has been successfully stored into the database.',
  'your data has been successfully deleted from the database.' => 'Your data has been successfully deleted from the database.',
  'your data has been failed to delete from the database. Please try again !' => 'Your data has been failed to delete from the database. Please try again !',
  'your data has been failed to store into the database. please try again !' => 'Your data has been failed to store into the database. Please try again !',
  'create FB chat embed code' => 'create FB chat embed code',
  'your reply has been sent successfully!' => 'Your reply has been sent successfully!',
  'no data to show!' => 'No data to show!',
  'notifications manager - page list' => 'Notifications manager - Page list',
  'you have not enabled messenger manager for any page yet !' => 'You have not enabled messenger manager for any page yet !',
  'notifications of inbox manager enabled pages' => 'Notifications of inbox manager enabled pages',
  'refresh data' => 'Refresh Data',
  'notifications' => 'Notifications',
  'no data to show !' => 'No data to show !',
  'go to fb' => 'Go to FB',
  'import account' => 'Import Account',
  'import lead' => 'Import Lead',
  'lead generator' => 'Lead Generator',
  '\'send message\' button' => '\'Send Message\' Button',
  'call to action poster' => 'Call to action Poster',
  'facebook chat plugin' => 'Facebook Chat Plugin',
  'page inbox & notification' => 'Page Inbox & Notification',
  'message dashboard' => 'Message Dashboard',
  'notification dashboard' => 'Notification Dashboard',
  'total message sent' => 'Total Message Sent',
  'total campaign created' => 'Total Campaign Created',
  'type your message here...' => 'Type your message here...',
  'time' => 'Time',
  'put a name so that you can identify it later' => 'Put a name so that you can identify it later',
  'Message may contain texts, urls and emotions.You can include #LEAD_USER_NAME# variable by clicking \'Include Lead User Name\' button. The variable will be replaced by real names when we will send it. If you want to show links or youtube video links with preview, then you can use \'Paste URL\' OR \'Paste Youtube Video URL\' fields below. Remember that if you put url/link inside this message area, preview of \'Paste URL\' OR \'Paste Youtube Video ID\' will not work. Then, the first url inside this message area will be previewed.' => 'Message may contain texts, urls and emotions.You can include #LEAD_USER_NAME# variable by clicking \'Include Lead User Name\' button. The variable will be replaced by real names when we will send it. If you want to show links or youtube video links with preview, then you can use \'Paste URL\' OR \'Paste Youtube Video URL\' fields below. Remember that if you put url/link inside this message area, preview of \'Paste URL\' OR \'Paste Youtube Video ID\' will not work. Then, the first url inside this message area will be previewed.',
  'include lead user last name' => 'Include lead user last name',
  'Paste any url, make sure your url contains http:// or https://. This url will be attched after your message with preview.' => 'Paste any url, make sure your url contains http:// or https://. This url will be attched after your message with preview.',
  'Eiher url or video will be previewed and attached at the bottom of message' => 'Either url or video will be previewed and attached at the bottom of message',
  'paste youtube video url' => 'Paste Youtube Video URL',
  'Paste any Youtube video URL, make sure your youtube url looks like https://www.youtube.com/watch?v=VIDEO_ID or https://youtu.be/VIDEO_ID. This video url will be attched after your message with preview.' => 'Paste any Youtube video URL, make sure your youtube url looks like https://www.youtube.com/watch?v=VIDEO_ID or https://youtu.be/VIDEO_ID. This video url will be attched after your message with preview.',
  'please select' => 'Please Select',
  'sorry, your monthly limit to send message is exceeded.' => 'Sorry, your monthly limit to send message is exceeded.',
  'Sorry, your monthly limit to create campaign is exceeded. You can not create another campaign this month.' => 'Sorry, your monthly limit to create campaign is exceeded. You can not create another campaign this month.',
  'The list seems large. We highly recommend to split your campaign with small campaign with 300 leads per campaign.For create custom campaign,' => 'The list seems large. We highly recommend to split your campaign with small campaign with 300 leads per campaign.For create custom campaign,',
  'go here' => 'go here',
  'Anyway we will submit all leads for sending message. But it may happen that facebook prevent sending message to high volume at a time. Use dealy 10 or more for safety.' => 'Anyway we will submit all leads for sending message. But it may happen that facebook prevent sending message to high volume at a time. Use dealy 10 or more for safety.',
  'please type a message or paste url/video url. system can not send blank message.' => 'Please type a message or paste url/video url. System can not send blank message.',
  'please select pages to create inbox campaign.' => 'Please select pages to create inbox campaign.',
  'campaign have been submitted successfully.' => 'Campaign have been submitted successfully.',
  'see report' => 'See report',
  'campaign status' => 'Campaign Status',
  'Choose one or more pages to create campaign. This message will send to all your active leads of pages you choose now. You can use \'Do not send message to these leads\' field below to unlist any list only from this campaign. To unlist any specific lead permanently, please go to \'Import Lead > Lead List\' and unsubscribe the lead, he/she will not recieve any other message until he/she is subscribed again. System will filter multiple instances of same lead for same campaign, means one user will not recieve same campaign message multiple times.' => 'Choose one or more pages to create campaign. This message will send to all your active leads of pages you choose now. You can use \'Do not send message to these leads\' field below to unlist any list only from this campaign. To unlist any specific lead permanently, please go to \'Import Lead > Lead List\' and unsubscribe the lead, he/she will not receive any other message until he/she is subscribed again. System will filter multiple instances of same lead for same campaign, means one user will not receive same campaign message multiple times.',
  'You can choose one or more. The leads you choose here will be unlisted form this campaign and will not recieve this message. Start typing a lead name, it\'s auto-complete.' => 'You can choose one or more. The leads you choose here will be unlisted form this campaign and will not receive this message. Start typing a lead name, it\'s auto-complete.',
  'do not send message to these leads' => 'Do not send message to these leads',
  'delay time' => 'Delay time',
  'Delay time is the delay between two successive message send. It is very important because without a delay time facebook may treat bulk sending as spam.   Keep it \'0\' to get random delay.' => 'Delay time is the delay between two successive message send. It is very important because without a delay time facebook may treat bulk sending as spam.   Keep it \'0\' to get random delay.',
  'embed unsubscribe link with message' => 'Embed unsubscribe link with message',
  'You can either send message now or can schedule it later. If you want to sed later the schedule it and system will automatically process this campaign as time and time zone mentioned. Schduled campaign may take upto 1 hour lomger than your schedule time depending on server\'s processing..' => 'You can either send message now or can schedule it later. If you want to sed later the schedule it and system will automatically process this campaign as time and time zone mentioned. Schduled campaign may take upto 1 hour lomger than your schedule time depending on server\'s processing..',
  'select date and time when you want to process this campaign.' => 'Select date and time when you want to process this campaign.',
  'include lead user name' => 'Include lead user name',
  'You can embed \'unsubscribe link\' with the message you send. Just enable it and system will automaticallly add the link at the bottom. Clicking the link will unsubscribe the lead. You can use your own method to serve this purpose if you want.' => 'You can embed \'unsubscribe link\' with the message you send. Just enable it and system will automaticallly add the link at the bottom. Clicking the link will unsubscribe the lead. You can use your own method to serve this purpose if you want.',
  'edit multi-page campaign' => 'Edit Multi-page Campaign',
  'Choose one or more pages to create campaign. This message will send to all your active leads of pages you choose now. You can use \'Do not send message to these leads\' field below to unlist any list only from this campaign. To unlist any specific lead permanently, please go to \'Import Lead > Lead List\' and unsubscribe the lead, he/she will not recieve any other message until he/she is subscribed again. The value of \'Total Lead Selected\' label may different than the original create form, because lead counts are dynamic and change over time.' => 'Choose one or more pages to create campaign. This message will send to all your active leads of pages you choose now. You can use \'Do not send message to these leads\' field below to unlist any list only from this campaign. To unlist any specific lead permanently, please go to \'Import Lead > Lead List\' and unsubscribe the lead, he/she will not receive any other message until he/she is subscribed again. The value of \'Total Lead Selected\' label may different than the original create form, because lead counts are dynamic and change over time.',
  'embed unsusbcribe link with message' => 'Embed unsusbcribe link with message',
  'server will consider your time zone when it process the campaign.' => 'Server will consider your time zone when it process the campaign.',
  'please choose any lead to send test message.' => 'Please choose any lead to send test message.',
  'campaign have been updated successfully.' => 'Campaign have been updated successfully.',
  'edit campaign' => 'Edit Campaign',
  'controller' => 'controller',
  'this campaign is in processing state' => 'This campaign is in processing state',
  'delete this campaign' => 'Delete this campaign',
  'multipage' => 'Multipage',
  'custom' => 'Custom',
  'not attachemnt' => 'Not attachemnt',
  'view campaign report' => 'View campaign report',
  'report' => 'Report',
  'only pending campaigns are editable' => 'Only pending campaigns are editable',
  'edit' => 'Edit',
  'message id' => 'Message ID',
  'url is invalid.' => 'URL is invalid.',
  'youtube url is invalid.' => 'Youtube URL is invalid.',
  'Choose one or more groups to create campaign. This message will send to all your active leads of groups you choose now. You can use \'Do not send message to these leads\' field below to unlist any list only from this campaign. To unlist any specific lead permanently, please go to \'Import Lead > Lead List\' and unsubscribe the lead, he/she will not recieve any other message until he/she is subscribed again. System will filter multiple instances of same lead for same campaign, means one user will not recieve same campaign message multiple times.' => 'Choose one or more groups to create campaign. This message will send to all your active leads of groups you choose now. You can use \'Do not send message to these leads\' field below to unlist any list only from this campaign. To unlist any specific lead permanently, please go to \'Import Lead > Lead List\' and unsubscribe the lead, he/she will not receive any other message until he/she is subscribed again. System will filter multiple instances of same lead for same campaign, means one user will not receive same campaign message multiple times.',
  'Delay time is the delay between two successive message send. It is very important because without a delay time facebook may treat bulk sending as spam. Keep it \'0\' to get random delay.' => 'Delay time is the delay between two successive message send. It is very important because without a delay time facebook may treat bulk sending as spam. Keep it \'0\' to get random delay.',
  'choose groups' => 'Choose Groups',
  'Please select groups to create inbox campaign.' => 'Please select groups to create inbox campaign.',
  'edit multi-group campaign' => 'Edit Multi-group Campaign',
  'custom campaign : lead list' => 'Custom Campaign : Lead List',
  'create new campaign' => 'Create New Campaign',
  'custom campaign' => 'Custom Campaign',
  'You can create a bulk message campaign by selecting any lead group you want. Select your leads and and click \'Create New Campaign\' button. System will filter multiple instances of same lead for same campaign, means one user will not recieve same campaign message multiple times.' => 'You can create a bulk message campaign by selecting any lead group you want. Select your leads and and click \'Create New Campaign\' button. System will filter multiple instances of same lead for same campaign, means one user will not receive same campaign message multiple times.',
  'client user name' => 'Client User Name',
  'all page' => 'All Page',
  'new custom campaign' => 'New Custom Campaign',
  'you have selected' => 'You have selected',
  'edit custom campaign' => 'Edit Custom Campaign',
  'You can include #LEAD_USER_NAME# variable inside your message. The variable will be replaced by real names when we will send it.' => 'You can include #LEAD_USER_NAME# variable inside your message. The variable will be replaced by real names when we will send it.',
  'Please select one or more (up to' => 'Please select one or more (up to',
  ') leads to create custom campaign.' => ') leads to create custom campaign.',
  'search lead' => 'Search Lead',
  'generate your'      => 'Generate Your',
  're-generate your'       => 'Re-generate Your',
  'campaign completed'       => 'Campaign Completed',
  'not scheduled'      => 'Not scheduled',
  'this campaign is in processing state'       => 'This campaign is in processing state',
  'campaign was marked as spam.'       => 'Campaign was marked as spam.',
  'no data found for campaign'       => 'No data found for campaign',
  'successfully sent'      => 'Successfully sent',
  'message out of'       => 'message out of',
  'something went wrong for one or more message. Original error message :'       => 'Something went wrong for one or more message. Original error message :',
  'sl.'      => 'SL.',
  'client username'      => 'Client Username',
  'sent at'      => 'Sent at',
  'page name'      => 'Page Name',
  'message ID / status'      => 'Message ID / Status',
  'original message :'       => 'Original Message :'
);