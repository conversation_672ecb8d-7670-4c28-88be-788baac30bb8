<?php 

$lang = array(
	"enable comment by post ID" => "Enable by Post ID",
	"edit comment by post ID" => "Edit by Post ID",
	"Enable Auto Comment" => "Enable Auto Comment",
	"Edit Auto Comment" => "Edit Auto Comment",
	"Auto Private Reply Campaign" => "Auto Private Reply Campaign",
	"Auto Comment Campaign" => "Auto Comment Campaign",
	"Edit User" => "Edit User",
	"Change User Password" => "Change User Password",
	"Company Settings" => "Company Settings",
	"Auto Reply Settings" => "Auto Reply Settings",
	"Product Settings" => "Product Settings",
	"Logo & Favicon Settings" => "Logo & Favicon Settings",
	"Bulk Message Sending Settings" => "Bulk Message Sending Settings",
	"Enter your email and password" => "Enter your email and password",
	"Click here" => "Click here",
	"Forgot your password?" => "Forgot your password?",
	"You are just one click away" => "You are just one click away",
	"By clicking sign up, you agree to the terms of use set out by this site" => "By clicking sign up, you agree to the terms of use set out by this site",
	"Today's Report" => "Today's Report",
	"Expired" => "Expired",
	"Unlink this account" => "Unlink this account",
	"Remove" => "Remove",
	"Deletion Report" => "Deletion Report",
	"Enable/Disable Report" => "Enable/Disable Report",
	"Inbox Scan Report" => "Inbox Scan Report",
	"Alert" => "Alert",
	"You have not selected any lead to delete." => "You have not selected any lead to delete.",
	"URL is invalid." => "URL is invalid.",
	"Youtube URL is invalid." => "Youtube URL is invalid.",
	"Resend from where it is left off" => "Resend from where it is left off",
	"Use Approved Facebook App of Author?" => "Use Approved Facebook App of Author?",
	"All needed permissions are not approved for your app" => "All needed permissions are not approved for your app",
	"No description found" => "No description found",
	"Force HTTPS" => "Force HTTPS",
	"Post ID" => "Post ID",
	"Auto Reply : Page List" => "Auto Reply : Page List",
	"Please provide a post ID" => "Please provide a post ID",
	"Please give the following information for post auto reply" => "Please Give The Following Information For Post Auto Reply",
	"Prefered" => "Prefered",
	"Upload an image or put an image url directly here" => "Upload an image or put an image url directly here",
	"Choose image from above or put a image url directly here" => "Choose image from above or put a image url directly here",
	"Auto Update System" => "Auto Update System",
	"Secret Code" => "Secret Code",
	"Facebook ID" => "Facebook ID",
	"You did not provide any reply message" => "You did not provide any reply message",
	"Text/Image/Link/Video Poster" => "Text/Image/Link/Video Poster",
	"CTA Poster" => "CTA Poster",
	"Offer Poster" => "Offer Poster",
	"Carousel/Slider Poster" => "Carousel/Slider Poster",
	"once per five minutes" => "Once per 5 minutes",
	"Visit" => "Visit",
	"Embed Code" => "Embed Code",
	"Embed code is only available for video posts." => "Embed code is only available for video posts.",
	"Auto Reply Template" => "Auto Reply Template",
	"Please select a template" => "Please select a template",
	"Text" => "Text",
	"Please type a message to post." => "Please type a message to post.",
	"Please paste an image url or uplaod an image to post." => "Please paste an image url or uplaod an image to post.",
	"Please paste an video url or uplaod an video to post." => "Please paste an video url or uplaod an video to post.",
	"Click here to see report" => "Click here to see report",
	"Video URL is invalid or this video is restricted from playback on certain sites." => "Video URL is invalid or this video is restricted from playback on certain sites.",
	"Processing..." => "Processing...",
	"Update Campaign Status" => "Update Campaign Status",
	"Facebook post information has been updated successfully." => "Facebook post information has been updated successfully.",
	"Facebook post has been performed successfully." => "Facebook post has been performed successfully.",
	"Facebook post campaign has been created successfully." => "Facebook post campaign has been created successfully.",
	"Facebook CTA post has been performed successfully." => "Facebook CTA post has been performed successfully.",
	"Facebook CTA post campaign has been created successfully." => "Facebook CTA post campaign has been created successfully.",
	"Copy this embed code" => "Copy this embed code",
	"sorry, your monthly limit is exceeded for this module." => "Sorry, your monthly limit is exceeded for this module.",
	"Sorry, your package does not allow carousel offer" => "Sorry, your package does not allow carousel offer",
	"Sorry, your package does not allow image/video offer" => "Sorry, your package does not allow image/video offer",
	"click here to see usage log" => "click here to see usage log",
	"New Post" => "New Post",
	"Embed code is only available for published video posts." => "Embed code is only available for published video posts.",
	"Back-end Theme" => "Back-end Theme",
	"Front-end Theme" => "Front-end Theme",
	"Application Name" => "Application Name",
	"Application Short Name" => "Application Short Name",
	"Application Slogan" => "Application Slogan",
	"Brand Settings" => "Brand Settings",
	"Preference Settings" => "Preference Settings",
	"Confirm" => "Confirm",
	"Abort" => "Abort",
	"Done" => "Done",
	"Multiple File Drag & Drop is not allowed." => "Multiple File Drag & Drop is not allowed.",
	"is not allowed. Allowed extensions: " => "is not allowed. Allowed extensions: ",
	"is not allowed. File already exists." => "is not allowed. File already exists.",
	"is not allowed. Allowed Max size: " => "is not allowed. Allowed Max size: ",
	"Upload is not allowed" => "Upload is not allowed",
	"is not allowed. Maximum allowed files are:" => "is not allowed. Maximum allowed files are:",
	"Drag & Drop Files" => "Drag & Drop Files",
	"frontend settings" => "Frontend Settings",
	"social Settings" => "Social Settings",
	"review settings" => "Review Settings",
	"display review block" => "Display Review Block",
	"customer review video" => "Customer Review Video",
	"review no: " => "Review #",
	"designation" => "Designation",
	"image" => "Image",
	"review" => "Review",
	"video Settings" => "Video Settings",
	"display tutorial block" => "Display Tutorial Block",
	"promo video" => "Promo Video",
	"tutorial no: " => "Tutorial #",
	"url" => "URL",
	"Contact & Location" => "Contact & Location",
	"keywords in comma separated" => "keywords in comma separated",
	"postback id" => "Postback ID",
	"Bot Name" => "Bot Name",
	"reply image" => "Reply Image",
	"reply audio" => "Reply Audio",
	"reply message" => "Reply Message",
	"reply video" => "Reply Video",
	"reply file" => "Reply File",
	"Valid" => "Valid",
	"Invalid" => "Invalid",
	"Only pending campaigns are editable" => "Only pending campaigns are editable",
	"just now" => "just now",
	"every minute" => "Every minute",
	"campaign has been restarted successfully." => "Campaign has been restarted successfully.",
	"Regular Push notification will make a sound and display a phone notification. Use it for important messages." => "Regular Push notification will make a sound and display a phone notification. Use it for important messages.",
	"Silent Push notification will display a phone notification without sound. Use it for regular messages that do not require immediate action." => "Silent Push notification will display a phone notification without sound. Use it for regular messages that do not require immediate action.",
	"No push will not display any notification. Use it for silently sending content updates." => "No push will not display any notification. Use it for silently sending content updates.",
	"campaign information has been saved successfully." => "Campaign information has been saved successfully.",
	"If you select Yes, you may skip to add your own app. You can use Author's app. But this option only for the admin only. This can't be used for other system users. User management feature will be disapeared." => "If you select Yes, you may skip to add your own app. You can use Author's app. But this option only for the admin only. This can't be used for other system users. User management feature will be disapeared.",
	"If select No , you will need to add your own app & get approval and system users can use it." => "If select No , you will need to add your own app & get approval and system users can use it.",
	"private reply" => "Private Reply",
	"comment reply" => "Comment Reply",
	"comment hidden" => "Comment Hidden",
	"comment deleted" => "Comment Deleted",
	"posted at" => "Posted at",
	"last replied" => "Last replied",
	"Please enable auto share or auto like." => "Please enable auto share or auto like.",
	"Please select page for auto sharing." => "Please select page for auto sharing.",
	"Please select page for auto liking." => "Please select page for auto liking.",
	"Edit Auto Like/Share Settings" => "Edit Auto Like/Share Settings",
	"auto like" => "Auto Like",
	"auto share" => "Auto Share",
	"Auto Like Report" => "Auto Like Report",
	"persistent menu copyright URL" => "Persistent menu copyright URL",
	"User login type" => "User login type",
	"persistent menu copyright text" => "Persistent menu copyright text",
	"I want manage_page permission approval. Use plain login" => "I want manage_page permission approval. Use plain login",
	"I do not want manage_page permission approval. Keep user as tester of my app" => "I do not want manage_page permission approval. Keep user as tester of my app",
	"Edit Bot" => "Edit Bot",
	"Delete Log" => "Delete Log",
	"Delete Bot" => "Delete Bot",
	"Email, Phone & Location" => "Email, Phone & Location",
	"Are you sure that you want to remove persistent menu from Facebook?" => "Are you sure that you want to remove persistent menu from Facebook?",
	"Default persistent menu can not be deleted" => "Default persistent menu can not be deleted",
	"Do you want to detete this template?." => "Do you want to detete this template?.",
	"Template Delete Confirmation" => "Template Delete Confirmation",
	"Template has been deleted successfully." => "Template has been deleted successfully.",
	"You can not delete this template because it is being used in the following bots. First make sure that these templates are free to delete. You can do this by editing or deleting the following bots." => "You can not delete this template because it is being used in the following bots. First make sure that these templates are free to delete. You can do this by editing or deleting the following bots.",
	"Please whitelist the domain to your Facebook Page:" => "Please whitelist the domain to your Facebook Page:",
	"Step 1 : Go to page Settings" => "Step 1 : Go to page Settings",
	"Step 2 : Go to Messenger Platform" => "Step 2 : Go to Messenger Platform",
	"Step 3 : Scroll down to Whitelisted Domains block and add your domain" => "Step 3 : Scroll down to Whitelisted Domains block and add your domain",
	"sorry! your data does not exist." => "Sorry! your data does not exist.",
	"Auto Comment template" => "Auto Comment Template",
	"Auto Comment Report" => "Auto Comment Report",
	"Auto Comment Template Manager" => "Auto Comment Template Manager",
	"Create New Template" => "Create New Template",
	"Template Name" => "Template Name",
	"Please Give The Following Information For Post Auto Comment" => "Please Give The Following Information For Post Auto Comment",
	"Your Template Name" => "Your Template Name",
	"add more" => "Add more",
	"add comments" => "Write your comment here...",
	"remove" => "Remove",
	"report of auto comment" => "Report of Auto Comment",
	"comment id" => "Comment ID",
	"comment status" => "Comment Status",
	"schedule type" => "Schedule Type",
	"Auto comment on post Cron Job Command [once per 1 minute or higher]" => "Auto comment on post Cron Job Command [once per 1 minute or higher]",
	"Auto Comment reply - Page list" => "Auto Comment Reply - Page List",
	"total auto comment enabled post" => "Total Auto Comment Enabled Post",
	"total auto comment sent" => "Total Auto Comment Sent",
	"last auto comment sent" => "Last Auto Comment Sent",
	"get latest posts & enable auto comment" => "Get Latest Posts & Enable Auto Comment",
	"Auto comment campaign name" => "Auto Comment Campaign Name",
	"Select Template From Template Manager" => "Select Template From Template Manager",
	"Select Schedule Time For Auto Comment comment" => "Select Schedule Time For Auto Comment comment",
	"Periodic Schedule time" => "Periodic Schedule time",
	"Please Select Periodic Time Schedule" => "Please Select Periodic Time Schedule",
	"Campaign Start time" => "Campaign Start time",
	"Campaign End time" => "Campaign End time",
	"Comment Between Time" => "Comment Between Time",
	"Auto Comment Type" => "Auto Comment Type",
	"Select Schedule type For Auto Comment" => "Select Schedule type For Auto Comment",
	"Set the allowed time of the comment. As example you want to auto comment by page from 10 AM to 8 PM. You don't want to comment other time. So set it 10:00 & 20:00" => "Set the allowed time of the comment. As example you want to auto comment by page from 10 AM to 8 PM. You don't want to comment other time. So set it 10:00 & 20:00",
	"Onetime campaign will comment only the first comment of the selected template and periodic campaign will auto comment multiple time periodically as per your settings." => "Onetime campaign will comment only the first comment of the selected template and periodic campaign will auto comment multiple time periodically as per your settings.",
	"Random type will pick a comment from template randomly each time and serial type will pick the comment serially from selected template first to last." => "Random type will pick a comment from template randomly each time and serial type will pick the comment serially from selected template first to last.",
	"Choose how frequently you want to comment" => "Choose how frequently you want to comment",
	"you have not select any option." => "You have not select any option.",
	"you have not select any option yet." => "You have not select any option yet.",
	"you have not select any template." => "You have not select any template.",
	"You have not Type auto campaign name" => "You have not Type auto campaign name",
	"You have not choose any schedule type" => "You have not choose any schedule type",
	"You have not select any schedule time" => "You have not select any schedule time",
	"You have not select any time zone" => "You have not select any time zone",
	"You have not select any periodic time" => "You have not select any periodic time",
	"You have not choose campaign start time" => "You have not choose campaign start time",
	"You have not choose campaign end time" => "You have not choose campaign end time",
	"Bulk message sent" => "Bulk message sent",
	"Private reply sent" => "Private reply sent",
	"Post publish" => "Post publish",
	"7 days report" => "7 days report",
	"Auto reply sent" => "Auto reply sent",
	"All time report" => "All time report",
	"Post published" => "Post published",
	"Last 7 days message sending report" => "Last 7 days message sending report",
	"Last 7 days private reply sent report" => "Last 7 days private reply sent report",
	"Last 7 days comment reply sent report" => "Last 7 days comment reply sent report",
	"Recently completed campaign (Bulk Message Campaign)" => "Recently completed campaign (Bulk Message Campaign)",
	"message sent" => "Message Sent",
	"Upcoming campaign (Bulk Message Campaign)" => "Upcoming campaign (Bulk Message Campaign)",
	"selected lead" => "Selected Lead",
	"Last seven days published post comparison" => "Last seven days published post comparison",
	"Today's post publishing report" => "Today's post publishing report",
	"Auto-reply enabled campaign" => "Auto-reply enabled campaign",
	"Scheduled bulk messaging campaign" => "Scheduled bulk messaging campaign",
	"Scheduled posting campaign" => "Scheduled posting campaign",
	"Carousel Post" => "Carousel Post",
	"Slider Post" => "Slider Post",
	"Recently completed campaign (Facebook Poster)" => "Recently completed campaign (Facebook Poster)",
	"post type" => "Post Type",
	"published at" => "Published at",
	"Upcoming campaign (Facebook Poster)" => "Upcoming campaign (Facebook Poster)",
	"Comment reply Sent" => "Comment reply Sent",
	"Please select comment between times." => "Please select comment between times.",
	"Comment between start time must be less than end time." => "Comment between start time must be less than end time.",
	"Image/Multi-Images" => "Image/Multi-Images",
	"auto_campaign_default_name" => "Default Campaign Name",
	"default_comment" => "",
	"default_comment_message" => "",
	
);

?>