<?php
/**
 * System messages translation for CodeIgniter(tm)
 * <AUTHOR> community
 * @copyright	Copyright (c) 2014 - 2016, British Columbia Institute of Technology (http://bcit.ca/)
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	http://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');
$lang['is_exist']			= "এই <b>%s</b> ne pas exister"; // User-defind (Created by Alamin)
$lang['required']              = "Le champ <b>%s</b> est requis.";
$lang['isset']                 = "Le champ <b>%s</b> doit avoir une valeur.";
$lang['valid_email']           = "Le champ <b>%s</b> doit contenir une adresse email valide.";
$lang['valid_emails']          = "Le champ <b>%s</b> ne peut contenir que des adresses email valides.";
$lang['valid_url']             = "Le champ <b>%s</b> doit contenir une URL valide.";
$lang['valid_ip']              = "Le champ <b>%s</b> doit contenir une IP valide.";
$lang['min_length']            = "Le champ <b>%s</b> doit contenir au moins <b>%s</b> caractères.";
$lang['max_length']            = "Le champ <b>%s</b> ne peut contenir plus de <b>%s</b> caractères.";
$lang['exact_length']          = "Le champ <b>%s</b> doit contenir exactement <b>%s</b> caractères.";
$lang['alpha']                 = "Le champ <b>%s</b> ne peut contenir que des caractères alphabétiques.";
$lang['alpha_numeric']         = "Le champ <b>%s</b> ne peut contenir que des caractères alphanumériques.";
$lang['alpha_numeric_spaces']  = "Le champ <b>%s</b> ne peut contenir que des caractères alphanumériques et des espaces.";
$lang['alpha_dash']            = "Le champ <b>%s</b> ne peut contenir que des caractères alphanumériques, des caractères de soulignement et des traits d'union.";
$lang['numeric']               = "Le champ <b>%s</b> doit contenir un nombre (caractères numériques).";
$lang['is_numeric']            = "Le champ <b>%s</b> ne peut contenir que de signes du type nombre.";
$lang['integer']               = "Le champ <b>%s</b> doit contenir un nombre entier.";
$lang['regex_match']           = "Le champ <b>%s</b> n'utilise pas le bon format.";
$lang['matches']               = "Le champ <b>%s</b> doit correspondre au champ <b>%s</b>.";
$lang['differs']               = "Le champ <b>%s</b> doit être différent du champ <b>%s</b>.";
$lang['is_unique']             = "Le champ <b>%s</b> doit contenir une valeur unique.";
$lang['is_natural']            = "Le champ <b>%s</b> ne peut contenir que des nombres positifs.";
$lang['is_natural_no_zero']    = "Le champ <b>%s</b> ne peut contenir que des nombres plus grands que zéro.";
$lang['decimal']               = "Le champ <b>%s</b> doit contenir un nombre décimal.";
$lang['less_than']             = "Le champ <b>%s</b> doit contenir un nombre inférieur à <b>%s</b>.";
$lang['less_than_equal_to']    = "Le champ <b>%s</b> doit contenir un nombre inférieur ou égal à <b>%s</b>.";
$lang['greater_than']          = "Le champ <b>%s</b> doit contenir un nombre supérieur à <b>%s</b>.";
$lang['greater_than_equal_to'] = "Le champ <b>%s</b> doit contenir un nombre supérieur ou égal à <b>%s</b>.";
$lang['error_message_not_set'] = "Impossible d'accéder à un message d'erreur correspondant à votre champ nommé <b>%s</b>.";
$lang['in_list']               = "Le champ <b>%s</b> doit avoir une de ces valeurs : <b>%s</b>.";
