<?php $this->load->view('admin/theme/message'); ?>

<?php 
// Month names mapping
$month_names = array(
    1 => 'January', 2 => 'February', 3 => 'March', 4 => 'April',
    5 => 'May', 6 => 'June', 7 => 'July', 8 => 'August',
    9 => 'September', 10 => 'October', 11 => 'November', 12 => 'December'
);

$no_limit_modules = array();
$not_monthly_modules = array();
foreach($usage_info as $module) {
    if($module['limit_enabled'] == '0')
        $no_limit_modules[] = $module['module_id'];
    
    if($module['extra_text'] == '')
        $not_monthly_modules[] = $module['module_id'];
}
?>

<!-- Main content -->
<section class="content-header">
    <h1 class='text-info'>
        <i class="fa fa-history"></i> 
        <?php echo $this->lang->line("View Usage Log"); ?> : 
        <?php echo $user_name; ?> 
        (<?php echo $month_names[$current_month] . "-" . $current_year; ?>)
    </h1>
    <ol class="breadcrumb">
        <li><a href="<?php echo site_url('agents'); ?>"><i class="fa fa-users"></i> <?php echo $this->lang->line("user management"); ?></a></li>
        <li class="active"><?php echo $this->lang->line("View Usage Log"); ?></li>
    </ol>
</section>

<section class="content">  
    <div class="row">
        <div class="col-xs-12">
            
            <!-- User Information Box -->
            <div class="box box-info">
                <div class="box-header with-border">
                    <h3 class="box-title">
                        <i class="fa fa-user"></i> <?php echo $this->lang->line("User Information"); ?>
                    </h3>
                </div>
                <div class="box-body">
                    <div class="row">
                        <div class="col-md-6">
                            <strong><?php echo $this->lang->line("name"); ?>:</strong> <?php echo $user_name; ?>
                        </div>
                        <div class="col-md-6">
                            <strong><?php echo $this->lang->line("email"); ?>:</strong> <?php echo $user_email; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Month/Year Filter -->
            <div class="box box-default">
                <div class="box-header with-border">
                    <h3 class="box-title">
                        <i class="fa fa-filter"></i> <?php echo $this->lang->line("Filter"); ?>
                    </h3>
                </div>
                <div class="box-body">
                    <form method="GET" action="<?php echo site_url('agents/view_usage_logs/' . $user_id); ?>" class="form-inline">
                        <div class="form-group">
                            <label for="month"><?php echo $this->lang->line("Month"); ?>:</label>
                            <select name="month" id="month" class="form-control">
                                <?php for($i = 1; $i <= 12; $i++): ?>
                                    <option value="<?php echo $i; ?>" <?php echo ($i == $current_month) ? 'selected' : ''; ?>>
                                        <?php echo $month_names[$i]; ?>
                                    </option>
                                <?php endfor; ?>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="year"><?php echo $this->lang->line("Year"); ?>:</label>
                            <select name="year" id="year" class="form-control">
                                <?php for($year = date('Y'); $year >= 2020; $year--): ?>
                                    <option value="<?php echo $year; ?>" <?php echo ($year == $current_year) ? 'selected' : ''; ?>>
                                        <?php echo $year; ?>
                                    </option>
                                <?php endfor; ?>
                            </select>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="fa fa-search"></i> <?php echo $this->lang->line("Filter"); ?>
                        </button>
                    </form>
                </div>
            </div>

            <!-- Usage Log Table -->
            <div class="box box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title">
                        <i class="fa fa-list"></i> <?php echo $this->lang->line("usage log"); ?> - 
                        <?php echo $month_names[$current_month] . " " . $current_year; ?>
                    </h3>
                </div>
                <div class="box-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr class="info">
                                    <th class="text-center">#</th>
                                    <th><?php echo $this->lang->line("Modules"); ?></th>
                                    <th class="text-center"><?php echo $this->lang->line("Usage Count"); ?></th>
                                    <th class="text-center"><?php echo $this->lang->line("Period"); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php 
                                $i = 0;
                                if (!empty($usage_info)):
                                    foreach($usage_info as $row):
                                        $i++;
                                        $usage_count = 0;
                                        $period_text = "";
                                        
                                        // Determine usage count and period
                                        if (in_array($row["module_id"], $not_monthly_modules)) {
                                            // Non-monthly modules - show lifetime usage
                                            if (isset($non_monthly_usage[$row["module_id"]])) {
                                                $usage_count = $non_monthly_usage[$row["module_id"]];
                                            }
                                            $period_text = $this->lang->line("Lifetime");
                                        } else if (in_array($row["module_id"], $no_limit_modules)) {
                                            // No limit modules
                                            $usage_count = "N/A";
                                            $period_text = "N/A";
                                        } else {
                                            // Monthly modules
                                            if (isset($row["usage_count"])) {
                                                $usage_count = $row["usage_count"];
                                            }
                                            $period_text = $this->lang->line("Monthly");
                                        }
                                ?>
                                <tr>
                                    <td class="text-center"><?php echo $i; ?></td>
                                    <td><?php echo $row["module_name"]; ?></td>
                                    <td class="text-center">
                                        <?php echo $usage_count; ?>
                                    </td>
                                    <td class="text-center">
                                        <?php echo $period_text; ?>
                                    </td>
                                </tr>
                                <?php 
                                    endforeach;
                                else:
                                ?>
                                <tr>
                                    <td colspan="4" class="text-center">
                                        <em><?php echo $this->lang->line("No usage data found for this period"); ?></em>
                                    </td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="box-footer">
                    <a href="<?php echo site_url('agents'); ?>" class="btn btn-default">
                        <i class="fa fa-arrow-left"></i> <?php echo $this->lang->line("Back to User Management"); ?>
                    </a>
                </div>
            </div>

        </div>        
    </div> 
</section>

<style>
    .table th {
        background-color: #f4f4f4;
        font-weight: bold;
    }
    .form-inline .form-group {
        margin-right: 15px;
    }
    .box-title {
        font-size: 16px;
        font-weight: bold;
    }
</style>
